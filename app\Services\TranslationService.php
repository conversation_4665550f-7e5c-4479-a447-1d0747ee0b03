<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class TranslationService
{
    private int $timeout;
    private int $cacheMinutes;

    public function __construct()
    {
        $this->timeout = 30;
        $this->cacheMinutes = 60; // Cache translations for 1 hour
    }

    /**
     * Translate text to English using free translation service
     */
    public function translateToEnglish(string $text, string $sourceLanguage = 'ar'): string
    {
        try {
            // Skip translation if already in English
            if ($sourceLanguage === 'en' || $this->isEnglish($text)) {
                return $text;
            }

            $cacheKey = 'translate_to_en_' . md5($text . $sourceLanguage);
            
            // Check cache first
            if ($cached = Cache::get($cacheKey)) {
                Log::info('Translation cache hit (to English)', [
                    'source_lang' => $sourceLanguage,
                    'text_length' => strlen($text)
                ]);
                return $cached;
            }

            // Use free translation service
            $translated = $this->translateWithFreeService($text, $sourceLanguage, 'en');
            
            // Cache the result
            Cache::put($cacheKey, $translated, now()->addMinutes($this->cacheMinutes));

            Log::info('Text translated to English', [
                'source_lang' => $sourceLanguage,
                'original_length' => strlen($text),
                'translated_length' => strlen($translated)
            ]);

            return $translated;

        } catch (\Exception $e) {
            Log::warning('Translation to English failed', [
                'error' => $e->getMessage(),
                'text' => substr($text, 0, 100)
            ]);
            
            // Return original text if translation fails
            return $text;
        }
    }

    /**
     * Translate text from English to target language
     */
    public function translateFromEnglish(string $text, string $targetLanguage = 'ar'): string
    {
        try {
            // Skip translation if target is English
            if ($targetLanguage === 'en') {
                return $text;
            }

            $cacheKey = 'translate_from_en_' . md5($text . $targetLanguage);
            
            // Check cache first
            if ($cached = Cache::get($cacheKey)) {
                Log::info('Translation cache hit (from English)', [
                    'target_lang' => $targetLanguage,
                    'text_length' => strlen($text)
                ]);
                return $cached;
            }

            // Use free translation service
            $translated = $this->translateWithFreeService($text, 'en', $targetLanguage);
            
            // Cache the result
            Cache::put($cacheKey, $translated, now()->addMinutes($this->cacheMinutes));

            Log::info('Text translated from English', [
                'target_lang' => $targetLanguage,
                'original_length' => strlen($text),
                'translated_length' => strlen($translated)
            ]);

            return $translated;

        } catch (\Exception $e) {
            Log::warning('Translation from English failed', [
                'error' => $e->getMessage(),
                'text' => substr($text, 0, 100)
            ]);
            
            // Return original text if translation fails
            return $text;
        }
    }

    /**
     * Translate using free translation service (MyMemory API)
     */
    private function translateWithFreeService(string $text, string $sourceLang, string $targetLang): string
    {
        try {
            // Use MyMemory free translation API
            $response = Http::timeout($this->timeout)
                ->get('https://api.mymemory.translated.net/get', [
                    'q' => $text,
                    'langpair' => $sourceLang . '|' . $targetLang,
                    'de' => '<EMAIL>' // Email for better rate limits
                ]);

            if (!$response->successful()) {
                throw new \Exception('Translation API request failed');
            }

            $data = $response->json();
            
            if (isset($data['responseData']['translatedText'])) {
                return $data['responseData']['translatedText'];
            }

            throw new \Exception('Invalid translation response format');

        } catch (\Exception $e) {
            Log::warning('Free translation service failed', [
                'error' => $e->getMessage(),
                'source_lang' => $sourceLang,
                'target_lang' => $targetLang
            ]);

            // Fallback: Try simple word-by-word translation for basic cases
            return $this->basicTranslation($text, $sourceLang, $targetLang);
        }
    }

    /**
     * Basic fallback translation for common phrases
     */
    private function basicTranslation(string $text, string $sourceLang, string $targetLang): string
    {
        // Basic Arabic to English translations
        if ($sourceLang === 'ar' && $targetLang === 'en') {
            $translations = [
                'مرحبا' => 'hello',
                'مرحباً' => 'hello',
                'شكرا' => 'thank you',
                'شكراً' => 'thank you',
                'ابحث عن' => 'search for',
                'ما هو' => 'what is',
                'ما هي' => 'what is',
                'كيف' => 'how',
                'متى' => 'when',
                'أين' => 'where',
                'لماذا' => 'why',
                'من' => 'who',
                'نعم' => 'yes',
                'لا' => 'no',
                'الذكاء الاصطناعي' => 'artificial intelligence',
                'تعلم الآلة' => 'machine learning',
                'البرمجة' => 'programming',
                'الحاسوب' => 'computer',
                'الإنترنت' => 'internet',
                'التكنولوجيا' => 'technology'
            ];

            foreach ($translations as $arabic => $english) {
                $text = str_replace($arabic, $english, $text);
            }
        }

        // Basic English to Arabic translations
        if ($sourceLang === 'en' && $targetLang === 'ar') {
            $translations = [
                'hello' => 'مرحباً',
                'thank you' => 'شكراً',
                'search for' => 'ابحث عن',
                'what is' => 'ما هو',
                'how' => 'كيف',
                'when' => 'متى',
                'where' => 'أين',
                'why' => 'لماذا',
                'who' => 'من',
                'yes' => 'نعم',
                'no' => 'لا',
                'artificial intelligence' => 'الذكاء الاصطناعي',
                'machine learning' => 'تعلم الآلة',
                'programming' => 'البرمجة',
                'computer' => 'الحاسوب',
                'internet' => 'الإنترنت',
                'technology' => 'التكنولوجيا',
                'Search results for:' => 'نتائج البحث عن:',
                'Source:' => 'المصدر:',
                'Total results:' => 'إجمالي النتائج:',
                'Internet search completed successfully!' => 'تم البحث بنجاح عبر الإنترنت!',
                'Feel free to ask more questions' => 'يمكنك طرح المزيد من الأسئلة',
                'Sorry, an error occurred' => 'عذراً، حدث خطأ',
                'Please try again' => 'يرجى المحاولة مرة أخرى'
            ];

            foreach ($translations as $english => $arabic) {
                $text = str_ireplace($english, $arabic, $text);
            }
        }

        return $text;
    }

    /**
     * Check if text is primarily in English
     */
    private function isEnglish(string $text): bool
    {
        // Simple heuristic: if more than 70% of characters are ASCII, consider it English
        $asciiCount = 0;
        $totalCount = mb_strlen($text, 'UTF-8');
        
        for ($i = 0; $i < $totalCount; $i++) {
            $char = mb_substr($text, $i, 1, 'UTF-8');
            if (ord($char) < 128) {
                $asciiCount++;
            }
        }
        
        return $totalCount > 0 && ($asciiCount / $totalCount) > 0.7;
    }

    /**
     * Get supported language codes
     */
    public function getSupportedLanguages(): array
    {
        return [
            'ar' => 'Arabic',
            'en' => 'English',
            'fr' => 'French',
            'es' => 'Spanish',
            'de' => 'German',
            'it' => 'Italian',
            'pt' => 'Portuguese',
            'ru' => 'Russian',
            'ja' => 'Japanese',
            'ko' => 'Korean',
            'zh' => 'Chinese'
        ];
    }
}
