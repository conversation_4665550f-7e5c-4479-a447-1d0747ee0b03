/**
 * WIDDX AI - Advanced Styles
 * Enhanced animations and effects
 */

/* Advanced Animations */
@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

/* Animation Classes */
.animate-slide-in-left {
    animation: slideInFromLeft 0.5s ease-out;
}

.animate-slide-in-right {
    animation: slideInFromRight 0.5s ease-out;
}

.animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

.animate-heartbeat {
    animation: heartbeat 1.5s ease-in-out infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

/* Hover Effects */
.hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(249, 115, 22, 0.5);
}

.hover-scale {
    transition: transform 0.2s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* Ripple Effect */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
    width: 300px;
    height: 300px;
}

/* Glass Morphism Enhanced */
.glass-enhanced {
    backdrop-filter: blur(20px) saturate(180%);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-enhanced-dark {
    backdrop-filter: blur(20px) saturate(180%);
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Gradient Backgrounds */
.gradient-widdx {
    background: linear-gradient(135deg, #f97316, #ea580c, #dc2626);
}

.gradient-widdx-soft {
    background: linear-gradient(135deg, #fed7aa, #fdba74, #fb923c);
}

.gradient-text {
    background: linear-gradient(135deg, #f97316, #ea580c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Advanced Shadows */
.shadow-widdx {
    box-shadow: 0 4px 20px rgba(249, 115, 22, 0.3);
}

.shadow-widdx-lg {
    box-shadow: 0 8px 30px rgba(249, 115, 22, 0.4);
}

.shadow-inner-widdx {
    box-shadow: inset 0 2px 4px rgba(249, 115, 22, 0.2);
}

/* Compact Mode */
.compact-mode .message-bubble {
    padding: 8px 12px;
    margin-bottom: 8px;
}

.compact-mode .feature-toggle {
    padding: 6px 12px;
    font-size: 12px;
}

.compact-mode #message-input {
    padding: 8px 12px;
}

/* No Animations Mode */
.no-animations * {
    animation: none !important;
    transition: none !important;
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Voice Recording Styles */
.voice-recording {
    position: relative;
}

.voice-recording::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border: 2px solid #ef4444;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: ripple 1s infinite;
}

/* Notification Styles */
.notification {
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: linear-gradient(135deg, #10b981, #059669);
}

.notification.error {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.notification.warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.notification.info {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

/* Progress Indicators */
.progress-ring {
    width: 40px;
    height: 40px;
}

.progress-ring-circle {
    stroke: #f97316;
    stroke-width: 3;
    fill: transparent;
    stroke-dasharray: 113;
    stroke-dashoffset: 113;
    transform-origin: 50% 50%;
    transform: rotate(-90deg);
    transition: stroke-dashoffset 0.3s ease;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .fab {
        bottom: 80px;
        right: 16px;
        width: 48px;
        height: 48px;
    }
    
    .hover-lift:hover {
        transform: none;
    }
    
    .tooltip::after {
        display: none;
    }
}

/* Dark Mode Enhancements */
.dark .glass-enhanced {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .shadow-widdx {
    box-shadow: 0 4px 20px rgba(249, 115, 22, 0.2);
}

/* Focus States */
.focus-widdx:focus {
    outline: none;
    ring: 2px;
    ring-color: #f97316;
    ring-opacity: 0.5;
}

/* Custom Scrollbar Enhanced */
.scrollbar-widdx::-webkit-scrollbar {
    width: 8px;
}

.scrollbar-widdx::-webkit-scrollbar-track {
    background: rgba(249, 115, 22, 0.1);
    border-radius: 4px;
}

.scrollbar-widdx::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #f97316, #ea580c);
    border-radius: 4px;
}

.scrollbar-widdx::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #ea580c, #dc2626);
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .feature-toggle.active {
        background: #000;
        color: #fff;
        border: 2px solid #fff;
    }
    
    .gradient-text {
        -webkit-text-fill-color: initial;
        color: #000;
    }
}

/* Print Styles */
@media print {
    .fab,
    .tooltip,
    .notification,
    #file-upload-modal,
    #emoji-modal {
        display: none !important;
    }
    
    .message-bubble {
        break-inside: avoid;
    }
}
