{{-- Settings Panel Component --}}
<div id="settings-panel" class="settings-panel hidden">
    <div class="settings-overlay" onclick="closeSettingsPanel()"></div>
    <div class="settings-content">
        {{-- Header --}}
        <div class="settings-header">
            <h2 class="settings-title">
                <i class="fas fa-cog"></i>
                <span data-lang="settings_title">إعدادات WIDDX</span>
            </h2>
            <button class="settings-close" onclick="closeSettingsPanel()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        {{-- Loading State --}}
        <div id="settings-loading" class="settings-loading">
            <div class="loading-spinner"></div>
            <p data-lang="loading_settings">جاري تحميل الإعدادات...</p>
        </div>

        {{-- Settings Content --}}
        <div id="settings-body" class="settings-body hidden">
            {{-- Feature Toggles Section --}}
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="fas fa-magic"></i>
                    <span data-lang="advanced_features">المميزات المتقدمة</span>
                </h3>
                <div class="section-description" data-lang="features_description">
                    تحكم في المميزات المتقدمة لـ WIDDX AI
                </div>
                
                <div id="feature-toggles" class="feature-toggles">
                    {{-- Feature toggles will be populated by JavaScript --}}
                </div>
            </div>

            {{-- Language Settings --}}
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="fas fa-language"></i>
                    <span data-lang="language_settings">إعدادات اللغة</span>
                </h3>
                
                <div class="setting-item">
                    <label class="setting-label" data-lang="interface_language">لغة الواجهة</label>
                    <select id="interface-language" class="setting-select">
                        <option value="ar">العربية</option>
                        <option value="en">English</option>
                        <option value="fr">Français</option>
                        <option value="es">Español</option>
                    </select>
                </div>

                <div class="setting-item">
                    <label class="setting-label" data-lang="response_language">لغة الردود</label>
                    <select id="response-language" class="setting-select">
                        <option value="auto">تلقائي (حسب لغة الرسالة)</option>
                        <option value="ar">العربية دائماً</option>
                        <option value="en">English Always</option>
                    </select>
                </div>

                <div class="setting-item">
                    <label class="setting-toggle">
                        <input type="checkbox" id="translation-enabled">
                        <span class="toggle-slider"></span>
                        <span class="toggle-label" data-lang="enable_translation">تفعيل الترجمة التلقائية</span>
                    </label>
                </div>
            </div>

            {{-- UI Settings --}}
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="fas fa-palette"></i>
                    <span data-lang="ui_settings">إعدادات الواجهة</span>
                </h3>
                
                <div class="setting-item">
                    <label class="setting-label" data-lang="theme">المظهر</label>
                    <select id="theme-select" class="setting-select">
                        <option value="dark">المظهر الداكن</option>
                        <option value="light">المظهر الفاتح</option>
                        <option value="auto">تلقائي</option>
                    </select>
                </div>

                <div class="setting-item">
                    <label class="setting-toggle">
                        <input type="checkbox" id="compact-mode">
                        <span class="toggle-slider"></span>
                        <span class="toggle-label" data-lang="compact_mode">الوضع المضغوط</span>
                    </label>
                </div>

                <div class="setting-item">
                    <label class="setting-toggle">
                        <input type="checkbox" id="show-thinking-process">
                        <span class="toggle-slider"></span>
                        <span class="toggle-label" data-lang="show_thinking">إظهار عملية التفكير</span>
                    </label>
                </div>

                <div class="setting-item">
                    <label class="setting-toggle">
                        <input type="checkbox" id="show-search-sources">
                        <span class="toggle-slider"></span>
                        <span class="toggle-label" data-lang="show_sources">إظهار مصادر البحث</span>
                    </label>
                </div>

                <div class="setting-item">
                    <label class="setting-toggle">
                        <input type="checkbox" id="animation-enabled">
                        <span class="toggle-slider"></span>
                        <span class="toggle-label" data-lang="enable_animations">تفعيل الحركات</span>
                    </label>
                </div>
            </div>

            {{-- Behavior Settings --}}
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="fas fa-brain"></i>
                    <span data-lang="behavior_settings">إعدادات السلوك</span>
                </h3>
                
                <div class="setting-item">
                    <label class="setting-toggle">
                        <input type="checkbox" id="auto-suggest-features">
                        <span class="toggle-slider"></span>
                        <span class="toggle-label" data-lang="auto_suggest">اقتراح المميزات تلقائياً</span>
                    </label>
                </div>

                <div class="setting-item">
                    <label class="setting-toggle">
                        <input type="checkbox" id="remember-context">
                        <span class="toggle-slider"></span>
                        <span class="toggle-label" data-lang="remember_context">تذكر سياق المحادثة</span>
                    </label>
                </div>

                <div class="setting-item">
                    <label class="setting-toggle">
                        <input type="checkbox" id="proactive-assistance">
                        <span class="toggle-slider"></span>
                        <span class="toggle-label" data-lang="proactive_help">المساعدة الاستباقية</span>
                    </label>
                </div>

                <div class="setting-item">
                    <label class="setting-toggle">
                        <input type="checkbox" id="learning-mode">
                        <span class="toggle-slider"></span>
                        <span class="toggle-label" data-lang="learning_mode">وضع التعلم</span>
                    </label>
                </div>

                <div class="setting-item">
                    <label class="setting-label" data-lang="search_results_count">عدد نتائج البحث</label>
                    <select id="search-results-count" class="setting-select">
                        <option value="3">3 نتائج</option>
                        <option value="5">5 نتائج</option>
                        <option value="10">10 نتائج</option>
                        <option value="15">15 نتيجة</option>
                    </select>
                </div>
            </div>

            {{-- Privacy Settings --}}
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="fas fa-shield-alt"></i>
                    <span data-lang="privacy_settings">إعدادات الخصوصية</span>
                </h3>
                
                <div class="setting-item">
                    <label class="setting-toggle">
                        <input type="checkbox" id="save-conversation-history">
                        <span class="toggle-slider"></span>
                        <span class="toggle-label" data-lang="save_history">حفظ تاريخ المحادثات</span>
                    </label>
                </div>

                <div class="setting-item">
                    <label class="setting-toggle">
                        <input type="checkbox" id="analytics-enabled">
                        <span class="toggle-slider"></span>
                        <span class="toggle-label" data-lang="enable_analytics">تفعيل التحليلات</span>
                    </label>
                </div>

                <div class="setting-item">
                    <label class="setting-toggle">
                        <input type="checkbox" id="share-usage-data">
                        <span class="toggle-slider"></span>
                        <span class="toggle-label" data-lang="share_data">مشاركة بيانات الاستخدام</span>
                    </label>
                </div>

                <div class="setting-item">
                    <label class="setting-label" data-lang="data_retention">مدة الاحتفاظ بالبيانات</label>
                    <select id="data-retention-days" class="setting-select">
                        <option value="7">أسبوع واحد</option>
                        <option value="30">شهر واحد</option>
                        <option value="90">3 أشهر</option>
                        <option value="365">سنة واحدة</option>
                    </select>
                </div>
            </div>
        </div>

        {{-- Footer Actions --}}
        <div class="settings-footer">
            <div class="settings-actions">
                <button class="btn-secondary" onclick="exportSettings()" data-lang="export_settings">
                    <i class="fas fa-download"></i>
                    تصدير الإعدادات
                </button>
                <button class="btn-secondary" onclick="importSettings()" data-lang="import_settings">
                    <i class="fas fa-upload"></i>
                    استيراد الإعدادات
                </button>
                <button class="btn-danger" onclick="resetSettings()" data-lang="reset_settings">
                    <i class="fas fa-undo"></i>
                    إعادة تعيين
                </button>
                <button class="btn-primary" onclick="saveSettings()" data-lang="save_settings">
                    <i class="fas fa-save"></i>
                    حفظ الإعدادات
                </button>
            </div>
        </div>
    </div>
</div>

{{-- Hidden file input for import --}}
<input type="file" id="import-file-input" accept=".json" style="display: none;" onchange="handleImportFile(event)">

<style>
/* Settings Panel Styles */
.settings-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.settings-panel.hidden {
    display: none;
}

.settings-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(4px);
}

.settings-content {
    position: relative;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.settings-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    background: #111;
    border-bottom: 1px solid #333;
}

.settings-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #fff;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.settings-close {
    background: none;
    border: none;
    color: #999;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.2s;
}

.settings-close:hover {
    color: #fff;
    background: #333;
}

.settings-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: #999;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #333;
    border-top: 3px solid #1d4ed8;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.settings-body {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
}

.settings-section {
    margin-bottom: 2rem;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #fff;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.section-description {
    color: #999;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

.setting-item {
    margin-bottom: 1rem;
}

.setting-label {
    display: block;
    color: #ccc;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.setting-select {
    width: 100%;
    background: #222;
    border: 1px solid #444;
    border-radius: 6px;
    padding: 0.75rem;
    color: #fff;
    font-size: 0.9rem;
}

.setting-select:focus {
    outline: none;
    border-color: #1d4ed8;
}

.setting-toggle {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
}

.setting-toggle input[type="checkbox"] {
    display: none;
}

.toggle-slider {
    position: relative;
    width: 44px;
    height: 24px;
    background: #444;
    border-radius: 12px;
    transition: all 0.3s;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: #fff;
    border-radius: 50%;
    transition: all 0.3s;
}

.setting-toggle input:checked + .toggle-slider {
    background: #1d4ed8;
}

.setting-toggle input:checked + .toggle-slider::before {
    transform: translateX(20px);
}

.toggle-label {
    color: #ccc;
    font-weight: 500;
}

.feature-toggles {
    display: grid;
    gap: 1rem;
}

.feature-toggle {
    background: #222;
    border: 1px solid #333;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.2s;
}

.feature-toggle:hover {
    border-color: #444;
}

.feature-toggle.enabled {
    border-color: #1d4ed8;
    background: rgba(29, 78, 216, 0.1);
}

.feature-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.feature-name {
    color: #fff;
    font-weight: 600;
}

.feature-description {
    color: #999;
    font-size: 0.85rem;
    margin-bottom: 0.75rem;
}

.feature-best-for {
    color: #666;
    font-size: 0.8rem;
    font-style: italic;
}

.settings-footer {
    background: #111;
    border-top: 1px solid #333;
    padding: 1.5rem;
}

.settings-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary, .btn-danger {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.9rem;
}

.btn-primary {
    background: #1d4ed8;
    color: #fff;
}

.btn-primary:hover {
    background: #1e40af;
}

.btn-secondary {
    background: #374151;
    color: #fff;
}

.btn-secondary:hover {
    background: #4b5563;
}

.btn-danger {
    background: #dc2626;
    color: #fff;
}

.btn-danger:hover {
    background: #b91c1c;
}

/* Responsive */
@media (max-width: 768px) {
    .settings-content {
        width: 95%;
        max-height: 95vh;
    }
    
    .settings-actions {
        justify-content: center;
    }
    
    .btn-primary, .btn-secondary, .btn-danger {
        flex: 1;
        min-width: 120px;
    }
}
</style>
