<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WIDDX - Button Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">WIDDX Button Test</h1>
        
        <!-- Feature Toggles Test -->
        <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
            <h2 class="text-xl font-semibold mb-4">Feature Toggles Test</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button class="feature-toggle p-4 bg-gray-100 hover:bg-orange-500 hover:text-white rounded-lg transition-all duration-200" data-feature="search">
                    <i class="fas fa-search mr-2"></i>
                    Web Search
                </button>
                <button class="feature-toggle p-4 bg-gray-100 hover:bg-orange-500 hover:text-white rounded-lg transition-all duration-200" data-feature="thinkMode">
                    <i class="fas fa-brain mr-2"></i>
                    Think Mode
                </button>
                <button class="feature-toggle p-4 bg-gray-100 hover:bg-orange-500 hover:text-white rounded-lg transition-all duration-200" data-feature="imageGeneration">
                    <i class="fas fa-image mr-2"></i>
                    Image Generation
                </button>
            </div>
        </div>
        
        <!-- Chat Form Test -->
        <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
            <h2 class="text-xl font-semibold mb-4">Chat Form Test</h2>
            <form id="chat-form" class="space-y-4">
                <div class="flex space-x-4">
                    <input 
                        type="text" 
                        id="message-input" 
                        placeholder="Type a test message..." 
                        class="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    >
                    <button 
                        type="submit" 
                        id="send-button"
                        class="px-6 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
                    >
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </form>
        </div>
        
        <!-- New Chat Button Test -->
        <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
            <h2 class="text-xl font-semibold mb-4">New Chat Button Test</h2>
            <button id="new-chat-btn" class="w-full p-4 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors">
                <i class="fas fa-plus mr-2"></i>
                New Chat
            </button>
        </div>
        
        <!-- Messages Container -->
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <h2 class="text-xl font-semibold mb-4">Messages</h2>
            <div id="messages-container" class="space-y-4 max-h-64 overflow-y-auto">
                <div class="text-gray-500 text-center py-8">
                    No messages yet. Try sending a message or clicking a feature button!
                </div>
            </div>
        </div>
        
        <!-- Status Display -->
        <div class="bg-gray-800 text-white p-4 rounded-lg mt-8">
            <h3 class="font-semibold mb-2">Debug Console:</h3>
            <div id="debug-console" class="text-sm font-mono bg-black p-3 rounded max-h-32 overflow-y-auto">
                <div class="text-green-400">Ready for testing...</div>
            </div>
        </div>
    </div>

    <!-- Simple Test Script -->
    <script>
        // Debug function
        function debugLog(message) {
            const console = document.getElementById('debug-console');
            const div = document.createElement('div');
            div.className = 'text-green-400';
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            console.appendChild(div);
            console.scrollTop = console.scrollHeight;
        }

        // Test feature toggles
        document.querySelectorAll('.feature-toggle').forEach((button, index) => {
            button.addEventListener('click', function() {
                this.classList.toggle('bg-orange-500');
                this.classList.toggle('text-white');
                this.classList.toggle('bg-gray-100');
                
                const feature = this.dataset.feature;
                const isActive = this.classList.contains('bg-orange-500');
                
                debugLog(`Feature ${feature}: ${isActive ? 'ACTIVATED' : 'DEACTIVATED'}`);
                
                // Add to messages
                const messagesContainer = document.getElementById('messages-container');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'p-3 bg-blue-50 rounded-lg';
                messageDiv.innerHTML = `
                    <strong>Feature Toggle:</strong> ${feature} is now 
                    <span class="font-semibold ${isActive ? 'text-green-600' : 'text-red-600'}">
                        ${isActive ? 'ACTIVE' : 'INACTIVE'}
                    </span>
                `;
                messagesContainer.appendChild(messageDiv);
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            });
            
            debugLog(`Feature toggle ${index + 1} (${button.dataset.feature}) initialized`);
        });

        // Test chat form
        const chatForm = document.getElementById('chat-form');
        if (chatForm) {
            chatForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const input = document.getElementById('message-input');
                const message = input.value.trim();
                
                if (message) {
                    debugLog(`Message sent: "${message}"`);
                    
                    // Add to messages
                    const messagesContainer = document.getElementById('messages-container');
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'p-3 bg-green-50 rounded-lg';
                    messageDiv.innerHTML = `
                        <strong>You:</strong> ${message}<br>
                        <strong>WIDDX:</strong> Message received! You said: "${message}"
                    `;
                    messagesContainer.appendChild(messageDiv);
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                    
                    input.value = '';
                } else {
                    debugLog('Empty message - not sent');
                }
            });
            
            debugLog('Chat form initialized');
        }

        // Test new chat button
        const newChatBtn = document.getElementById('new-chat-btn');
        if (newChatBtn) {
            newChatBtn.addEventListener('click', function() {
                debugLog('New chat button clicked');
                
                // Reset all feature toggles
                document.querySelectorAll('.feature-toggle').forEach(button => {
                    button.classList.remove('bg-orange-500', 'text-white');
                    button.classList.add('bg-gray-100');
                });
                
                // Clear messages
                const messagesContainer = document.getElementById('messages-container');
                messagesContainer.innerHTML = `
                    <div class="text-gray-500 text-center py-8">
                        New chat started! All features reset.
                    </div>
                `;
                
                // Clear input
                const input = document.getElementById('message-input');
                if (input) input.value = '';
                
                debugLog('New chat started - all features reset');
            });
            
            debugLog('New chat button initialized');
        }

        debugLog('All test components initialized successfully!');
    </script>
</body>
</html>
