<?php

namespace App\Services;

use App\Models\DeepSearchHistory;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;

class UltraDeepSearchService
{
    private DeepSeekClient $deepSeekClient;
    private GeminiClient $geminiClient;
    private FreeSearchService $freeSearch;
    private TranslationService $translation;
    private FeatureToggleService $featureToggle;

    // Search sources
    public const SOURCE_WEB = 'web';
    public const SOURCE_NEWS = 'news';
    public const SOURCE_ACADEMIC = 'academic';
    public const SOURCE_KNOWLEDGE_BASE = 'knowledge_base';

    // Cache settings
    private int $cacheMinutes = 60;

    public function __construct(
        DeepSeekClient $deepSeekClient,
        GeminiClient $geminiClient,
        FreeSearchService $freeSearch,
        TranslationService $translation,
        FeatureToggleService $featureToggle
    ) {
        $this->deepSeekClient = $deepSeekClient;
        $this->geminiClient = $geminiClient;
        $this->freeSearch = $freeSearch;
        $this->translation = $translation;
        $this->featureToggle = $featureToggle;
    }

    /**
     * Perform ultra deep search
     */
    public function ultraDeepSearch(string $query, array $options = []): array
    {
        $startTime = microtime(true);
        $sessionId = $options['session_id'] ?? 'default';
        $userIdentifier = $options['user_identifier'] ?? $sessionId;

        try {
            // Check if ultra deep search is enabled for user
            if (!$this->featureToggle->isFeatureEnabled($userIdentifier, FeatureToggleService::FEATURE_ULTRA_DEEP_SEARCH)) {
                return [
                    'success' => false,
                    'content' => 'Ultra deep search is not enabled for your account.',
                    'error' => 'Feature not enabled'
                ];
            }

            // Get user settings for ultra deep search
            $settings = $this->featureToggle->getFeatureSettings($userIdentifier, FeatureToggleService::FEATURE_ULTRA_DEEP_SEARCH);

            // Process and enhance the query
            $processedQuery = $this->enhanceSearchQuery($query, $options);

            // Determine which sources to search
            $sources = $this->determineSearchSources($processedQuery, $settings);

            Log::info('Starting ultra deep search', [
                'session_id' => $sessionId,
                'original_query' => $query,
                'processed_query' => $processedQuery,
                'sources' => $sources,
                'max_sources' => $settings['max_sources'] ?? 10
            ]);

            // Search across multiple sources
            $searchResults = $this->searchMultipleSources($processedQuery, $sources, $settings, $options);

            // Analyze and synthesize results
            $synthesizedResult = $this->synthesizeResults($query, $searchResults, $settings, $options);

            // Calculate processing time
            $processingTime = (microtime(true) - $startTime) * 1000;

            // Record search history
            $this->recordSearchHistory($sessionId, $query, $processedQuery, $sources, $searchResults, $synthesizedResult, $processingTime);

            // Format response
            $response = $this->formatSearchResponse($synthesizedResult, $searchResults, $sources, $options);

            return [
                'success' => true,
                'content' => $response,
                'processing_time' => $processingTime,
                'metadata' => [
                    'original_query' => $query,
                    'processed_query' => $processedQuery,
                    'sources_searched' => $sources,
                    'total_sources' => count($searchResults),
                    'relevance_score' => $synthesizedResult['relevance_score'] ?? 0.0
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Ultra deep search failed', [
                'session_id' => $sessionId,
                'query' => $query,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'content' => 'Sorry, I encountered an error during the deep search. Please try again.',
                'error' => $e->getMessage(),
                'processing_time' => (microtime(true) - $startTime) * 1000
            ];
        }
    }

    /**
     * Enhance search query for better results
     */
    private function enhanceSearchQuery(string $query, array $options): string
    {
        try {
            // Use DeepSeek to enhance the query
            $systemPrompt = "You are a search query enhancement expert. Your task is to transform the user's query into a more effective search query that will yield better results. Focus on:
1. Identifying the core information need
2. Adding relevant keywords and synonyms
3. Removing unnecessary words
4. Structuring the query for search engines
5. Maintaining the original intent

Respond ONLY with the enhanced query, nothing else.";

            $messages = [
                ['role' => 'system', 'content' => $systemPrompt],
                ['role' => 'user', 'content' => "Enhance this search query: {$query}"]
            ];

            $response = $this->deepSeekClient->chat($messages, [
                'temperature' => 0.3,
                'max_tokens' => 100
            ]);

            if ($response['success']) {
                $enhancedQuery = trim($response['content']);

                Log::info('Query enhanced', [
                    'original' => $query,
                    'enhanced' => $enhancedQuery
                ]);

                return $enhancedQuery;
            }
        } catch (\Exception $e) {
            Log::warning('Query enhancement failed, using original query', [
                'error' => $e->getMessage()
            ]);
        }

        return $query;
    }

    /**
     * Determine which sources to search based on query
     */
    private function determineSearchSources(string $query, array $settings): array
    {
        $maxSources = min($settings['max_sources'] ?? 10, 10);
        $sources = [self::SOURCE_WEB]; // Always include web search

        // Check for academic/research indicators
        if (preg_match('/\b(research|study|paper|journal|academic|science|scientific|thesis|dissertation)\b/i', $query)) {
            $sources[] = self::SOURCE_ACADEMIC;
        }

        // Check for news/current events indicators
        if (preg_match('/\b(news|recent|latest|today|yesterday|week|month|year|current|update|event)\b/i', $query)) {
            $sources[] = self::SOURCE_NEWS;
        }

        // Check for knowledge base indicators
        if (preg_match('/\b(what is|definition|explain|concept|meaning|history|background|overview)\b/i', $query)) {
            $sources[] = self::SOURCE_KNOWLEDGE_BASE;
        }

        // Limit to max sources
        return array_slice(array_unique($sources), 0, $maxSources);
    }

    /**
     * Search across multiple sources
     */
    private function searchMultipleSources(string $query, array $sources, array $settings, array $options): array
    {
        $results = [];
        $timeout = $settings['search_timeout_seconds'] ?? 60;

        // Web search (always included)
        if (in_array(self::SOURCE_WEB, $sources)) {
            $webResults = $this->performWebSearch($query, $options);
            if ($webResults['success']) {
                $results[self::SOURCE_WEB] = $webResults;
            }
        }

        // News search
        if (in_array(self::SOURCE_NEWS, $sources)) {
            $newsResults = $this->performNewsSearch($query, $options);
            if ($newsResults['success']) {
                $results[self::SOURCE_NEWS] = $newsResults;
            }
        }

        // Academic search
        if (in_array(self::SOURCE_ACADEMIC, $sources)) {
            $academicResults = $this->performAcademicSearch($query, $options);
            if ($academicResults['success']) {
                $results[self::SOURCE_ACADEMIC] = $academicResults;
            }
        }

        // Knowledge base search
        if (in_array(self::SOURCE_KNOWLEDGE_BASE, $sources)) {
            $kbResults = $this->performKnowledgeBaseSearch($query, $options);
            if ($kbResults['success']) {
                $results[self::SOURCE_KNOWLEDGE_BASE] = $kbResults;
            }
        }

        return $results;
    }

    /**
     * Perform web search
     */
    private function performWebSearch(string $query, array $options): array
    {
        try {
            // Use the existing FreeSearchService
            $result = $this->freeSearch->search($query, array_merge($options, [
                'max_results' => 10
            ]));

            return [
                'success' => $result['success'],
                'results' => $result['results'] ?? [],
                'provider' => $result['provider'] ?? 'unknown',
                'total_results' => $result['total_results'] ?? 0
            ];
        } catch (\Exception $e) {
            Log::warning('Web search failed', [
                'query' => $query,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'results' => [],
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Perform news search
     */
    private function performNewsSearch(string $query, array $options): array
    {
        try {
            // Add news-specific terms to the query
            $newsQuery = $query . ' news recent';

            // Use the existing FreeSearchService with news focus
            $result = $this->freeSearch->search($newsQuery, array_merge($options, [
                'max_results' => 5
            ]));

            return [
                'success' => $result['success'],
                'results' => $result['results'] ?? [],
                'provider' => $result['provider'] ?? 'unknown',
                'total_results' => $result['total_results'] ?? 0
            ];
        } catch (\Exception $e) {
            Log::warning('News search failed', [
                'query' => $query,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'results' => [],
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Perform academic search
     */
    private function performAcademicSearch(string $query, array $options): array
    {
        try {
            // Add academic-specific terms to the query
            $academicQuery = $query . ' research paper study';

            // Use the existing FreeSearchService with academic focus
            $result = $this->freeSearch->search($academicQuery, array_merge($options, [
                'max_results' => 5
            ]));

            return [
                'success' => $result['success'],
                'results' => $result['results'] ?? [],
                'provider' => $result['provider'] ?? 'unknown',
                'total_results' => $result['total_results'] ?? 0
            ];
        } catch (\Exception $e) {
            Log::warning('Academic search failed', [
                'query' => $query,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'results' => [],
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Perform knowledge base search
     */
    private function performKnowledgeBaseSearch(string $query, array $options): array
    {
        try {
            // Add knowledge base specific terms to the query
            $kbQuery = 'what is ' . $query . ' definition explanation';

            // Use the existing FreeSearchService with knowledge base focus
            $result = $this->freeSearch->search($kbQuery, array_merge($options, [
                'max_results' => 5
            ]));

            return [
                'success' => $result['success'],
                'results' => $result['results'] ?? [],
                'provider' => $result['provider'] ?? 'unknown',
                'total_results' => $result['total_results'] ?? 0
            ];
        } catch (\Exception $e) {
            Log::warning('Knowledge base search failed', [
                'query' => $query,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'results' => [],
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Synthesize results from multiple sources
     */
    private function synthesizeResults(string $originalQuery, array $searchResults, array $settings, array $options): array
    {
        try {
            // Combine all results
            $allResults = [];
            $sourceInfo = [];

            foreach ($searchResults as $source => $sourceResults) {
                if (isset($sourceResults['results']) && is_array($sourceResults['results'])) {
                    foreach ($sourceResults['results'] as $result) {
                        $result['source'] = $source;
                        $allResults[] = $result;
                    }
                    $sourceInfo[$source] = count($sourceResults['results']);
                }
            }

            // If no results found
            if (empty($allResults)) {
                return [
                    'synthesized_content' => 'No relevant information found across all sources.',
                    'relevance_score' => 0.0,
                    'sources_used' => [],
                    'total_results' => 0
                ];
            }

            // Create synthesis prompt
            $synthesisPrompt = $this->buildSynthesisPrompt($originalQuery, $allResults, $settings);

            // Get AI synthesis
            $synthesis = $this->getAISynthesis($synthesisPrompt, $options);

            // Calculate relevance score
            $relevanceScore = $this->calculateRelevanceScore($allResults, $synthesis);

            return [
                'synthesized_content' => $synthesis,
                'relevance_score' => $relevanceScore,
                'sources_used' => array_keys($sourceInfo),
                'total_results' => count($allResults),
                'source_breakdown' => $sourceInfo
            ];

        } catch (\Exception $e) {
            Log::error('Result synthesis failed', [
                'query' => $originalQuery,
                'error' => $e->getMessage()
            ]);

            return [
                'synthesized_content' => 'Unable to synthesize search results due to an error.',
                'relevance_score' => 0.0,
                'sources_used' => [],
                'total_results' => 0
            ];
        }
    }

    /**
     * Build synthesis prompt for AI
     */
    private function buildSynthesisPrompt(string $query, array $results, array $settings): string
    {
        $prompt = "You are an expert information synthesizer. Your task is to analyze search results from multiple sources and create a comprehensive, accurate, and well-structured response.

Original Query: {$query}

Search Results from Multiple Sources:
";

        foreach ($results as $index => $result) {
            $source = $result['source'] ?? 'unknown';
            $title = $result['title'] ?? 'No title';
            $snippet = $result['snippet'] ?? 'No description';

            $prompt .= "\n" . ($index + 1) . ". [{$source}] {$title}\n   {$snippet}\n";
        }

        $prompt .= "\n\nInstructions:
1. Synthesize the information from all sources into a coherent response
2. Prioritize accuracy and relevance to the original query
3. Include key insights from different sources
4. Mention conflicting information if any
5. Structure the response clearly with proper formatting
6. Be comprehensive but concise
7. If sources disagree, present multiple perspectives

Please provide a well-structured synthesis of this information:";

        return $prompt;
    }

    /**
     * Get AI synthesis of search results
     */
    private function getAISynthesis(string $prompt, array $options): string
    {
        try {
            // Try DeepSeek first for synthesis
            $messages = [
                ['role' => 'user', 'content' => $prompt]
            ];

            $response = $this->deepSeekClient->chat($messages, [
                'temperature' => 0.5,
                'max_tokens' => 2000
            ]);

            if ($response['success']) {
                return $response['content'];
            }
        } catch (\Exception $e) {
            Log::warning('DeepSeek synthesis failed, trying Gemini', [
                'error' => $e->getMessage()
            ]);
        }

        // Fallback to Gemini
        try {
            $messages = [
                ['role' => 'user', 'content' => $prompt]
            ];

            $response = $this->geminiClient->chat($messages, [
                'temperature' => 0.5,
                'max_tokens' => 2000
            ]);

            if ($response['success']) {
                return $response['content'];
            }
        } catch (\Exception $e) {
            Log::error('Both AI models failed for synthesis', [
                'error' => $e->getMessage()
            ]);
        }

        // Fallback to basic summary
        return "Based on the search results, here's what I found about your query. Multiple sources were consulted to provide comprehensive information.";
    }

    /**
     * Calculate relevance score
     */
    private function calculateRelevanceScore(array $results, string $synthesis): float
    {
        // Simple relevance scoring based on:
        // 1. Number of results found
        // 2. Length of synthesis (indicates comprehensive information)
        // 3. Source diversity

        $resultCount = count($results);
        $synthesisLength = strlen($synthesis);
        $sources = array_unique(array_column($results, 'source'));
        $sourceDiversity = count($sources);

        // Normalize scores
        $resultScore = min($resultCount / 10, 1.0); // Max 10 results for full score
        $lengthScore = min($synthesisLength / 1000, 1.0); // Max 1000 chars for full score
        $diversityScore = min($sourceDiversity / 4, 1.0); // Max 4 sources for full score

        // Weighted average
        $relevanceScore = ($resultScore * 0.3) + ($lengthScore * 0.4) + ($diversityScore * 0.3);

        return round($relevanceScore, 2);
    }

    /**
     * Format search response for user
     */
    private function formatSearchResponse(array $synthesizedResult, array $searchResults, array $sources, array $options): string
    {
        $isArabic = ($options['target_language_code'] ?? 'en') === 'ar';

        if ($isArabic) {
            $response = "🔍 **البحث العميق المتقدم**\n\n";
            $response .= "**📊 مصادر البحث:** " . implode(', ', $sources) . "\n";
            $response .= "**📈 نقاط الصلة:** " . ($synthesizedResult['relevance_score'] * 100) . "%\n";
            $response .= "**📋 إجمالي النتائج:** " . $synthesizedResult['total_results'] . "\n\n";
            $response .= "**🎯 التحليل الشامل:**\n\n";
        } else {
            $response = "🔍 **Ultra Deep Search Results**\n\n";
            $response .= "**📊 Sources Searched:** " . implode(', ', $sources) . "\n";
            $response .= "**📈 Relevance Score:** " . ($synthesizedResult['relevance_score'] * 100) . "%\n";
            $response .= "**📋 Total Results:** " . $synthesizedResult['total_results'] . "\n\n";
            $response .= "**🎯 Comprehensive Analysis:**\n\n";
        }

        $response .= $synthesizedResult['synthesized_content'];

        // Add source breakdown if available
        if (!empty($synthesizedResult['source_breakdown'])) {
            if ($isArabic) {
                $response .= "\n\n**📚 تفصيل المصادر:**\n";
            } else {
                $response .= "\n\n**📚 Source Breakdown:**\n";
            }

            foreach ($synthesizedResult['source_breakdown'] as $source => $count) {
                $response .= "• {$source}: {$count} " . ($isArabic ? "نتيجة" : "results") . "\n";
            }
        }

        if ($isArabic) {
            $response .= "\n\n✅ **تم إجراء بحث شامل عبر مصادر متعددة لضمان الحصول على معلومات دقيقة ومحدثة.**";
        } else {
            $response .= "\n\n✅ **Comprehensive search completed across multiple sources to ensure accurate and up-to-date information.**";
        }

        return $response;
    }

    /**
     * Record search history to database
     */
    private function recordSearchHistory(string $sessionId, string $originalQuery, string $processedQuery, array $sources, array $searchResults, array $synthesizedResult, float $processingTime): void
    {
        try {
            DeepSearchHistory::recordSearch([
                'session_id' => $sessionId,
                'search_type' => 'ultra_deep',
                'original_query' => $originalQuery,
                'processed_query' => $processedQuery,
                'search_sources' => $sources,
                'raw_results' => $searchResults,
                'synthesized_result' => $synthesizedResult['synthesized_content'],
                'total_sources_searched' => count($sources),
                'processing_time_ms' => $processingTime,
                'relevance_score' => $synthesizedResult['relevance_score'],
                'was_successful' => true
            ]);
        } catch (\Exception $e) {
            Log::warning('Failed to record search history', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get search history for session
     */
    public function getSearchHistory(string $sessionId, int $limit = 10): array
    {
        return DeepSearchHistory::where('session_id', $sessionId)
            ->where('search_type', 'ultra_deep')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($search) {
                return [
                    'id' => $search->id,
                    'original_query' => $search->original_query,
                    'processed_query' => $search->processed_query,
                    'sources' => $search->search_sources,
                    'relevance_score' => $search->relevance_score,
                    'processing_time_ms' => $search->processing_time_ms,
                    'created_at' => $search->created_at->toISOString()
                ];
            })
            ->toArray();
    }

    /**
     * Get search statistics
     */
    public function getSearchStats(string $sessionId): array
    {
        $searches = DeepSearchHistory::where('session_id', $sessionId)
            ->where('search_type', 'ultra_deep')
            ->where('was_successful', true)
            ->get();

        if ($searches->isEmpty()) {
            return [
                'total_searches' => 0,
                'average_relevance' => 0.0,
                'average_processing_time' => 0.0,
                'most_used_sources' => [],
                'total_search_time' => 0.0
            ];
        }

        // Calculate most used sources
        $allSources = $searches->flatMap(fn($search) => $search->search_sources ?? []);
        $sourceUsage = $allSources->countBy()->sortDesc();

        return [
            'total_searches' => $searches->count(),
            'average_relevance' => round($searches->avg('relevance_score'), 2),
            'average_processing_time' => round($searches->avg('processing_time_ms'), 2),
            'most_used_sources' => $sourceUsage->take(5)->toArray(),
            'total_search_time' => round($searches->sum('processing_time_ms') / 1000, 2) // in seconds
        ];
    }
}
