<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('deep_search_history', function (Blueprint $table) {
            $table->id();
            $table->string('session_id')->index();
            $table->string('search_type')->index(); // 'deep', 'ultra_deep'
            $table->text('original_query');
            $table->text('processed_query');
            $table->json('search_sources'); // Array of sources searched
            $table->json('raw_results'); // Raw search results
            $table->longText('synthesized_result'); // Final processed result
            $table->integer('total_sources_searched')->default(0);
            $table->integer('processing_time_ms')->default(0);
            $table->float('relevance_score')->default(0.0);
            $table->boolean('was_successful')->default(false);
            $table->text('error_message')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('deep_search_history');
    }
};
