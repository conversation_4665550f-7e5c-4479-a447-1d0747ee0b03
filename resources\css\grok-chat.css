/* WIDDX AI - GROK Style Chat Interface */

/* ===== CHAT MESSAGES ===== */
.grok-messages {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.grok-message {
  display: flex;
  gap: var(--space-md);
  opacity: 0;
  animation: messageSlideIn 0.3s ease-out forwards;
}

.grok-message.user {
  flex-direction: row-reverse;
}

.grok-message-avatar {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
  margin-top: var(--space-xs);
}

.grok-message.user .grok-message-avatar {
  background: var(--bg-elevated);
  color: var(--text-primary);
  border: 2px solid var(--border-primary);
}

.grok-message.assistant .grok-message-avatar {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
}

.grok-message-content {
  flex: 1;
  min-width: 0;
}

.grok-message-bubble {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-md) var(--space-lg);
  color: var(--text-primary);
  line-height: 1.6;
  word-wrap: break-word;
  position: relative;
}

.grok-message.user .grok-message-bubble {
  background: var(--accent-primary);
  border-color: var(--accent-secondary);
  color: white;
}

.grok-message-meta {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-top: var(--space-sm);
  font-size: 12px;
  color: var(--text-tertiary);
}

.grok-message-time {
  color: var(--text-tertiary);
}

.grok-message-actions {
  display: flex;
  gap: var(--space-xs);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.grok-message:hover .grok-message-actions {
  opacity: 1;
}

.grok-message-action {
  width: 24px;
  height: 24px;
  border: none;
  background: var(--bg-hover);
  color: var(--text-tertiary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.grok-message-action:hover {
  background: var(--bg-active);
  color: var(--text-primary);
}

/* ===== THINKING INDICATOR ===== */
.grok-thinking {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-lg);
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  margin: var(--space-lg) 0;
}

.grok-thinking-avatar {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.grok-thinking-content {
  flex: 1;
}

.grok-thinking-text {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: var(--space-sm);
}

.grok-thinking-steps {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.grok-thinking-step {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  font-size: 13px;
}

.grok-thinking-step-icon {
  width: 16px;
  height: 16px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
}

.grok-thinking-step.active .grok-thinking-step-icon {
  background: var(--accent-primary);
  color: white;
}

.grok-thinking-step.completed .grok-thinking-step-icon {
  background: var(--success);
  color: white;
}

.grok-thinking-step.pending .grok-thinking-step-icon {
  background: var(--bg-hover);
  color: var(--text-tertiary);
}

/* ===== INPUT AREA ===== */
.grok-input-container {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.grok-input-wrapper {
  position: relative;
  display: flex;
  align-items: flex-end;
  gap: var(--space-md);
}

.grok-textarea-container {
  flex: 1;
  position: relative;
}

.grok-textarea {
  width: 100%;
  min-height: 52px;
  max-height: 200px;
  padding: var(--space-md) var(--space-lg);
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  color: var(--text-primary);
  font-family: inherit;
  font-size: 16px;
  line-height: 1.5;
  resize: none;
  outline: none;
  transition: all var(--transition-fast);
}

.grok-textarea:focus {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px var(--accent-light);
}

.grok-textarea::placeholder {
  color: var(--text-tertiary);
}

.grok-send-btn {
  width: 52px;
  height: 52px;
  background: var(--accent-primary);
  border: none;
  border-radius: var(--radius-full);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-md);
}

.grok-send-btn:hover:not(:disabled) {
  background: var(--accent-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.grok-send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* ===== WELCOME SCREEN ===== */
.grok-welcome {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: var(--space-2xl);
  max-width: 600px;
  margin: 0 auto;
}

.grok-welcome-title {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-md);
}

.grok-welcome-subtitle {
  font-size: 18px;
  color: var(--text-secondary);
  margin-bottom: var(--space-2xl);
  line-height: 1.6;
}

.grok-welcome-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-lg);
  width: 100%;
  margin-bottom: var(--space-2xl);
}

.grok-feature-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  transition: all var(--transition-normal);
  cursor: pointer;
}

.grok-feature-card:hover {
  background: var(--bg-hover);
  border-color: var(--border-secondary);
  transform: translateY(-2px);
}

.grok-feature-icon {
  width: 48px;
  height: 48px;
  background: var(--accent-light);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-md);
  color: var(--accent-primary);
  font-size: 24px;
}

.grok-feature-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.grok-feature-description {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* ===== ANIMATIONS ===== */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes thinkingPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.grok-thinking-dots {
  display: flex;
  gap: 4px;
}

.grok-thinking-dot {
  width: 6px;
  height: 6px;
  background: var(--accent-primary);
  border-radius: var(--radius-full);
  animation: thinkingPulse 1.4s ease-in-out infinite;
}

.grok-thinking-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.grok-thinking-dot:nth-child(3) {
  animation-delay: 0.4s;
}

/* ===== LANGUAGE DIRECTION SUPPORT ===== */
[dir="rtl"] .grok-message {
  direction: rtl;
}

[dir="rtl"] .grok-message.user {
  flex-direction: row;
}

[dir="rtl"] .grok-message.assistant {
  flex-direction: row-reverse;
}

[dir="ltr"] .grok-message {
  direction: ltr;
}

[dir="ltr"] .grok-message.user {
  flex-direction: row-reverse;
}

[dir="ltr"] .grok-message.assistant {
  flex-direction: row;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .grok-welcome {
    padding: var(--space-lg);
  }
  
  .grok-welcome-title {
    font-size: 24px;
  }
  
  .grok-welcome-subtitle {
    font-size: 16px;
  }
  
  .grok-welcome-features {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }
  
  .grok-message-bubble {
    padding: var(--space-md);
  }
  
  .grok-textarea {
    font-size: 16px; /* Prevent zoom on iOS */
  }
}
