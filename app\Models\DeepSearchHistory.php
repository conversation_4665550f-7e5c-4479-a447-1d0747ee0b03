<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DeepSearchHistory extends Model
{
    use HasFactory;

    protected $table = 'deep_search_history';

    protected $fillable = [
        'session_id',
        'search_type',
        'original_query',
        'processed_query',
        'search_sources',
        'raw_results',
        'synthesized_result',
        'total_sources_searched',
        'processing_time_ms',
        'relevance_score',
        'was_successful',
        'error_message'
    ];

    protected $casts = [
        'search_sources' => 'array',
        'raw_results' => 'array',
        'was_successful' => 'boolean',
        'relevance_score' => 'float'
    ];

    /**
     * Get recent search history for session
     */
    public static function getRecentSearches(string $sessionId, int $limit = 10)
    {
        return self::where('session_id', $sessionId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get successful searches by type
     */
    public static function getSuccessfulSearchesByType(string $searchType, int $limit = 50)
    {
        return self::where('search_type', $searchType)
            ->where('was_successful', true)
            ->orderBy('relevance_score', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Record new search
     */
    public static function recordSearch(array $searchData)
    {
        return self::create($searchData);
    }
}
