<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_preferences', function (Blueprint $table) {
            $table->id();
            $table->string('user_identifier')->index(); // Session ID or User ID
            $table->string('preference_key')->index(); // Feature name
            $table->json('preference_value'); // Feature settings
            $table->boolean('is_enabled')->default(false); // Feature enabled/disabled
            $table->text('description')->nullable(); // Optional description
            $table->timestamps();

            // Composite unique index
            $table->unique(['user_identifier', 'preference_key']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_preferences');
    }
};
