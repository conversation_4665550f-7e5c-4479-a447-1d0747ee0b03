/**
 * WIDDX AI Settings Management
 * Handles user preferences and feature toggles
 */

class SettingsManager {
    constructor() {
        this.apiBase = '/api';
        this.currentSettings = {};
        this.availableFeatures = {};
        this.isLoading = false;
        
        this.init();
    }

    async init() {
        // Load settings when page loads
        await this.loadSettings();
        this.bindEvents();
    }

    /**
     * Load all user settings
     */
    async loadSettings() {
        try {
            this.showLoading(true);
            
            // Load preferences and features in parallel
            const [preferencesResponse, featuresResponse] = await Promise.all([
                fetch(`${this.apiBase}/preferences/`),
                fetch(`${this.apiBase}/feature-toggles/available`)
            ]);

            if (preferencesResponse.ok && featuresResponse.ok) {
                const preferences = await preferencesResponse.json();
                const features = await featuresResponse.json();
                
                this.currentSettings = preferences.data;
                this.availableFeatures = features.data;
                
                this.populateSettings();
                this.populateFeatureToggles();
            } else {
                throw new Error('Failed to load settings');
            }
        } catch (error) {
            console.error('Error loading settings:', error);
            this.showError('فشل في تحميل الإعدادات');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Populate settings form with current values
     */
    populateSettings() {
        const prefs = this.currentSettings.preferences || {};
        
        // Language settings
        this.setSelectValue('interface-language', prefs.language?.interface_language || 'ar');
        this.setSelectValue('response-language', prefs.language?.response_language || 'auto');
        this.setCheckboxValue('translation-enabled', prefs.language?.translation_enabled !== false);
        
        // UI settings
        this.setSelectValue('theme-select', prefs.ui?.theme || 'dark');
        this.setCheckboxValue('compact-mode', prefs.ui?.compact_mode || false);
        this.setCheckboxValue('show-thinking-process', prefs.ui?.show_thinking_process !== false);
        this.setCheckboxValue('show-search-sources', prefs.ui?.show_search_sources !== false);
        this.setCheckboxValue('animation-enabled', prefs.ui?.animation_enabled !== false);
        
        // Behavior settings
        this.setCheckboxValue('auto-suggest-features', prefs.behavior?.auto_suggest_features !== false);
        this.setCheckboxValue('remember-context', prefs.behavior?.remember_conversation_context !== false);
        this.setCheckboxValue('proactive-assistance', prefs.behavior?.proactive_assistance !== false);
        this.setCheckboxValue('learning-mode', prefs.behavior?.learning_mode !== false);
        this.setSelectValue('search-results-count', prefs.behavior?.search_results_count || 5);
        
        // Privacy settings
        this.setCheckboxValue('save-conversation-history', prefs.privacy?.save_conversation_history !== false);
        this.setCheckboxValue('analytics-enabled', prefs.privacy?.analytics_enabled || false);
        this.setCheckboxValue('share-usage-data', prefs.privacy?.share_usage_data || false);
        this.setSelectValue('data-retention-days', prefs.privacy?.data_retention_days || 30);
    }

    /**
     * Populate feature toggles
     */
    populateFeatureToggles() {
        const container = document.getElementById('feature-toggles');
        const userFeatures = this.currentSettings.features || {};
        
        container.innerHTML = '';
        
        this.availableFeatures.forEach(feature => {
            const isEnabled = userFeatures[feature.key]?.enabled || false;
            const featureElement = this.createFeatureToggle(feature, isEnabled);
            container.appendChild(featureElement);
        });
    }

    /**
     * Create feature toggle element
     */
    createFeatureToggle(feature, isEnabled) {
        const div = document.createElement('div');
        div.className = `feature-toggle ${isEnabled ? 'enabled' : ''}`;
        div.dataset.featureKey = feature.key;
        
        div.innerHTML = `
            <div class="feature-header">
                <span class="feature-name">${this.getFeatureName(feature.key)}</span>
                <label class="setting-toggle">
                    <input type="checkbox" ${isEnabled ? 'checked' : ''} 
                           onchange="settingsManager.toggleFeature('${feature.key}', this.checked)">
                    <span class="toggle-slider"></span>
                </label>
            </div>
            <div class="feature-description">${feature.description}</div>
            <div class="feature-best-for">الأفضل لـ: ${this.getFeatureBestFor(feature.key)}</div>
        `;
        
        return div;
    }

    /**
     * Get localized feature name
     */
    getFeatureName(key) {
        const names = {
            'deep_thinking': 'التفكير العميق',
            'deep_search': 'البحث العميق',
            'ultra_deep_search': 'البحث الأعمق',
            'live_search': 'البحث المباشر',
            'advanced_personality': 'الشخصية المتقدمة',
            'image_generation': 'توليد الصور',
            'voice_mode': 'الوضع الصوتي',
            'document_analysis': 'تحليل المستندات',
            'vision_analysis': 'تحليل الصور'
        };
        return names[key] || key;
    }

    /**
     * Get feature best use cases
     */
    getFeatureBestFor(key) {
        const bestFor = {
            'deep_thinking': 'المسائل المعقدة والتحليل المتعمق',
            'deep_search': 'البحث في مصادر متعددة',
            'ultra_deep_search': 'البحث الشامل والتحليل المتقدم',
            'live_search': 'البحث السريع والمعلومات الحديثة',
            'advanced_personality': 'المحادثات الطبيعية والتفاعل المتقدم',
            'image_generation': 'إنشاء الصور والمحتوى البصري',
            'voice_mode': 'التفاعل الصوتي والمحادثات المنطوقة',
            'document_analysis': 'تحليل الملفات والمستندات',
            'vision_analysis': 'فهم وتحليل الصور'
        };
        return bestFor[key] || 'مميزات متقدمة';
    }

    /**
     * Toggle feature on/off
     */
    async toggleFeature(featureKey, enabled) {
        try {
            const endpoint = enabled ? 'enable' : 'disable';
            const response = await fetch(`${this.apiBase}/feature-toggles/${featureKey}/${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                }
            });

            if (response.ok) {
                // Update UI
                const featureElement = document.querySelector(`[data-feature-key="${featureKey}"]`);
                if (featureElement) {
                    featureElement.classList.toggle('enabled', enabled);
                }
                
                // Update local state
                if (!this.currentSettings.features) {
                    this.currentSettings.features = {};
                }
                if (!this.currentSettings.features[featureKey]) {
                    this.currentSettings.features[featureKey] = {};
                }
                this.currentSettings.features[featureKey].enabled = enabled;
                
                this.showSuccess(enabled ? 'تم تفعيل الميزة' : 'تم إلغاء تفعيل الميزة');
            } else {
                throw new Error('Failed to toggle feature');
            }
        } catch (error) {
            console.error('Error toggling feature:', error);
            this.showError('فشل في تغيير حالة الميزة');
            
            // Revert checkbox state
            const checkbox = document.querySelector(`[data-feature-key="${featureKey}"] input[type="checkbox"]`);
            if (checkbox) {
                checkbox.checked = !enabled;
            }
        }
    }

    /**
     * Save all settings
     */
    async saveSettings() {
        try {
            this.showLoading(true);
            
            const settings = this.collectSettings();
            const promises = [];
            
            // Save each category
            for (const [category, values] of Object.entries(settings)) {
                promises.push(
                    fetch(`${this.apiBase}/preferences/category/${category}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                        },
                        body: JSON.stringify({ preferences: values })
                    })
                );
            }
            
            const responses = await Promise.all(promises);
            const allSuccessful = responses.every(response => response.ok);
            
            if (allSuccessful) {
                this.showSuccess('تم حفظ الإعدادات بنجاح');
                // Update local state
                this.currentSettings.preferences = settings;
            } else {
                throw new Error('Some settings failed to save');
            }
        } catch (error) {
            console.error('Error saving settings:', error);
            this.showError('فشل في حفظ الإعدادات');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Collect current settings from form
     */
    collectSettings() {
        return {
            language: {
                interface_language: this.getSelectValue('interface-language'),
                response_language: this.getSelectValue('response-language'),
                translation_enabled: this.getCheckboxValue('translation-enabled')
            },
            ui: {
                theme: this.getSelectValue('theme-select'),
                compact_mode: this.getCheckboxValue('compact-mode'),
                show_thinking_process: this.getCheckboxValue('show-thinking-process'),
                show_search_sources: this.getCheckboxValue('show-search-sources'),
                animation_enabled: this.getCheckboxValue('animation-enabled')
            },
            behavior: {
                auto_suggest_features: this.getCheckboxValue('auto-suggest-features'),
                remember_conversation_context: this.getCheckboxValue('remember-context'),
                proactive_assistance: this.getCheckboxValue('proactive-assistance'),
                learning_mode: this.getCheckboxValue('learning-mode'),
                search_results_count: parseInt(this.getSelectValue('search-results-count'))
            },
            privacy: {
                save_conversation_history: this.getCheckboxValue('save-conversation-history'),
                analytics_enabled: this.getCheckboxValue('analytics-enabled'),
                share_usage_data: this.getCheckboxValue('share-usage-data'),
                data_retention_days: parseInt(this.getSelectValue('data-retention-days'))
            }
        };
    }

    /**
     * Export settings
     */
    async exportSettings() {
        try {
            const response = await fetch(`${this.apiBase}/preferences/export`);
            if (response.ok) {
                const data = await response.json();
                const blob = new Blob([JSON.stringify(data.data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                
                const a = document.createElement('a');
                a.href = url;
                a.download = `widdx-settings-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                this.showSuccess('تم تصدير الإعدادات بنجاح');
            } else {
                throw new Error('Export failed');
            }
        } catch (error) {
            console.error('Error exporting settings:', error);
            this.showError('فشل في تصدير الإعدادات');
        }
    }

    /**
     * Import settings
     */
    importSettings() {
        document.getElementById('import-file-input').click();
    }

    /**
     * Handle import file selection
     */
    async handleImportFile(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        try {
            const text = await file.text();
            const data = JSON.parse(text);
            
            const response = await fetch(`${this.apiBase}/preferences/import`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                },
                body: JSON.stringify({ export_data: data })
            });
            
            if (response.ok) {
                this.showSuccess('تم استيراد الإعدادات بنجاح');
                await this.loadSettings(); // Reload settings
            } else {
                throw new Error('Import failed');
            }
        } catch (error) {
            console.error('Error importing settings:', error);
            this.showError('فشل في استيراد الإعدادات');
        }
        
        // Clear file input
        event.target.value = '';
    }

    /**
     * Reset settings to defaults
     */
    async resetSettings() {
        if (!confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
            return;
        }
        
        try {
            const response = await fetch(`${this.apiBase}/preferences/reset`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                }
            });
            
            if (response.ok) {
                this.showSuccess('تم إعادة تعيين الإعدادات بنجاح');
                await this.loadSettings(); // Reload settings
            } else {
                throw new Error('Reset failed');
            }
        } catch (error) {
            console.error('Error resetting settings:', error);
            this.showError('فشل في إعادة تعيين الإعدادات');
        }
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Auto-save on change for some settings
        const autoSaveElements = [
            'interface-language',
            'response-language',
            'theme-select'
        ];
        
        autoSaveElements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => {
                    this.saveSettings();
                });
            }
        });
    }

    /**
     * Utility methods
     */
    setSelectValue(id, value) {
        const element = document.getElementById(id);
        if (element) element.value = value;
    }

    getSelectValue(id) {
        const element = document.getElementById(id);
        return element ? element.value : '';
    }

    setCheckboxValue(id, value) {
        const element = document.getElementById(id);
        if (element) element.checked = value;
    }

    getCheckboxValue(id) {
        const element = document.getElementById(id);
        return element ? element.checked : false;
    }

    showLoading(show) {
        const loading = document.getElementById('settings-loading');
        const body = document.getElementById('settings-body');
        
        if (loading && body) {
            loading.classList.toggle('hidden', !show);
            body.classList.toggle('hidden', show);
        }
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Add styles
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '6px',
            color: '#fff',
            fontWeight: '500',
            zIndex: '10000',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease',
            backgroundColor: type === 'success' ? '#10b981' : '#ef4444'
        });
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// Global functions for HTML onclick handlers
function openSettingsPanel() {
    document.getElementById('settings-panel').classList.remove('hidden');
}

function closeSettingsPanel() {
    document.getElementById('settings-panel').classList.add('hidden');
}

function saveSettings() {
    settingsManager.saveSettings();
}

function exportSettings() {
    settingsManager.exportSettings();
}

function importSettings() {
    settingsManager.importSettings();
}

function resetSettings() {
    settingsManager.resetSettings();
}

function handleImportFile(event) {
    settingsManager.handleImportFile(event);
}

// Initialize settings manager when DOM is loaded
let settingsManager;
document.addEventListener('DOMContentLoaded', () => {
    settingsManager = new SettingsManager();
});
