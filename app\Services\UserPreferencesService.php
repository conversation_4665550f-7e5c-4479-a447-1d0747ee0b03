<?php

namespace App\Services;

use App\Models\UserPreference;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;

class UserPreferencesService
{
    private FeatureToggleService $featureToggle;
    private int $cacheMinutes = 60;

    // Preference categories
    public const CATEGORY_FEATURES = 'features';
    public const CATEGORY_UI = 'ui';
    public const CATEGORY_LANGUAGE = 'language';
    public const CATEGORY_BEHAVIOR = 'behavior';
    public const CATEGORY_PRIVACY = 'privacy';

    // Default preferences
    private array $defaultPreferences = [
        'language' => [
            'interface_language' => 'en',
            'response_language' => 'auto', // auto-detect from user input
            'translation_enabled' => true
        ],
        'ui' => [
            'theme' => 'dark',
            'compact_mode' => false,
            'show_thinking_process' => true,
            'show_search_sources' => true,
            'animation_enabled' => true
        ],
        'behavior' => [
            'auto_suggest_features' => true,
            'remember_conversation_context' => true,
            'proactive_assistance' => true,
            'learning_mode' => true,
            'search_results_count' => 5,
            'auto_deep_search' => false
        ],
        'privacy' => [
            'save_conversation_history' => true,
            'analytics_enabled' => false,
            'share_usage_data' => false,
            'data_retention_days' => 30
        ]
    ];

    public function __construct(FeatureToggleService $featureToggle)
    {
        $this->featureToggle = $featureToggle;
    }

    /**
     * Get all user preferences
     */
    public function getAllPreferences(string $userIdentifier): array
    {
        $cacheKey = "user_preferences_all_{$userIdentifier}";

        return Cache::remember($cacheKey, now()->addMinutes($this->cacheMinutes), function () use ($userIdentifier) {
            // Get feature toggles
            $features = $this->featureToggle->getAllUserFeatures($userIdentifier);

            // Get other preferences
            $otherPreferences = [];
            foreach ($this->defaultPreferences as $category => $defaults) {
                $otherPreferences[$category] = $this->getCategoryPreferences($userIdentifier, $category);
            }

            return [
                'features' => $features,
                'preferences' => $otherPreferences,
                'user_identifier' => $userIdentifier,
                'last_updated' => now()->toISOString()
            ];
        });
    }

    /**
     * Get preferences for specific category
     */
    public function getCategoryPreferences(string $userIdentifier, string $category): array
    {
        $cacheKey = "user_preferences_{$category}_{$userIdentifier}";

        return Cache::remember($cacheKey, now()->addMinutes($this->cacheMinutes), function () use ($userIdentifier, $category) {
            $defaults = $this->defaultPreferences[$category] ?? [];
            $userPrefs = UserPreference::getUserPreference($userIdentifier, "category_{$category}", []);

            return array_merge($defaults, $userPrefs);
        });
    }

    /**
     * Set preferences for specific category
     */
    public function setCategoryPreferences(string $userIdentifier, string $category, array $preferences): bool
    {
        try {
            // Validate category
            if (!array_key_exists($category, $this->defaultPreferences)) {
                throw new \InvalidArgumentException("Invalid preference category: {$category}");
            }

            // Validate preferences structure
            $this->validatePreferences($category, $preferences);

            // Merge with defaults to ensure all required fields are present
            $defaults = $this->defaultPreferences[$category];
            $mergedPreferences = array_merge($defaults, $preferences);

            // Save to database
            UserPreference::setUserPreference(
                $userIdentifier,
                "category_{$category}",
                $mergedPreferences,
                true,
                "User preferences for {$category} category"
            );

            // Clear cache
            $this->clearUserCache($userIdentifier, $category);

            Log::info('User preferences updated', [
                'user_identifier' => $userIdentifier,
                'category' => $category,
                'preferences' => $mergedPreferences
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to set user preferences', [
                'user_identifier' => $userIdentifier,
                'category' => $category,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Get specific preference value
     */
    public function getPreference(string $userIdentifier, string $category, string $key, $default = null)
    {
        $categoryPrefs = $this->getCategoryPreferences($userIdentifier, $category);
        return $categoryPrefs[$key] ?? $default;
    }

    /**
     * Set specific preference value
     */
    public function setPreference(string $userIdentifier, string $category, string $key, $value): bool
    {
        try {
            $currentPrefs = $this->getCategoryPreferences($userIdentifier, $category);
            $currentPrefs[$key] = $value;

            return $this->setCategoryPreferences($userIdentifier, $category, $currentPrefs);

        } catch (\Exception $e) {
            Log::error('Failed to set specific preference', [
                'user_identifier' => $userIdentifier,
                'category' => $category,
                'key' => $key,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Initialize default preferences for new user
     */
    public function initializeUserPreferences(string $userIdentifier): void
    {
        try {
            // Initialize feature toggles
            $this->featureToggle->initializeUserFeatures($userIdentifier);

            // Initialize other preferences
            foreach ($this->defaultPreferences as $category => $defaults) {
                if (!UserPreference::where('user_identifier', $userIdentifier)
                    ->where('preference_key', "category_{$category}")
                    ->exists()) {

                    $this->setCategoryPreferences($userIdentifier, $category, $defaults);
                }
            }

            Log::info('User preferences initialized', [
                'user_identifier' => $userIdentifier
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to initialize user preferences', [
                'user_identifier' => $userIdentifier,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Export user preferences
     */
    public function exportPreferences(string $userIdentifier): array
    {
        try {
            $allPreferences = $this->getAllPreferences($userIdentifier);

            return [
                'export_version' => '1.0',
                'exported_at' => now()->toISOString(),
                'user_identifier' => $userIdentifier,
                'preferences' => $allPreferences
            ];

        } catch (\Exception $e) {
            Log::error('Failed to export user preferences', [
                'user_identifier' => $userIdentifier,
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Import user preferences
     */
    public function importPreferences(string $userIdentifier, array $exportData): bool
    {
        try {
            // Validate export data structure
            if (!isset($exportData['preferences'])) {
                throw new \InvalidArgumentException('Invalid export data structure');
            }

            $preferences = $exportData['preferences'];

            // Import feature toggles
            if (isset($preferences['features'])) {
                foreach ($preferences['features'] as $featureKey => $featureConfig) {
                    if ($featureConfig['enabled']) {
                        $this->featureToggle->enableFeature(
                            $userIdentifier,
                            $featureKey,
                            $featureConfig['settings'] ?? []
                        );
                    } else {
                        $this->featureToggle->disableFeature($userIdentifier, $featureKey);
                    }
                }
            }

            // Import other preferences
            if (isset($preferences['preferences'])) {
                foreach ($preferences['preferences'] as $category => $categoryPrefs) {
                    $this->setCategoryPreferences($userIdentifier, $category, $categoryPrefs);
                }
            }

            Log::info('User preferences imported successfully', [
                'user_identifier' => $userIdentifier
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to import user preferences', [
                'user_identifier' => $userIdentifier,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Reset user preferences to defaults
     */
    public function resetToDefaults(string $userIdentifier): bool
    {
        try {
            // Clear all existing preferences
            UserPreference::where('user_identifier', $userIdentifier)->delete();

            // Clear cache
            $this->clearAllUserCache($userIdentifier);

            // Reinitialize with defaults
            $this->initializeUserPreferences($userIdentifier);

            Log::info('User preferences reset to defaults', [
                'user_identifier' => $userIdentifier
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to reset user preferences', [
                'user_identifier' => $userIdentifier,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Get preference schema for validation
     */
    public function getPreferenceSchema(): array
    {
        return [
            'categories' => array_keys($this->defaultPreferences),
            'schemas' => [
                'language' => [
                    'interface_language' => 'string|in:en,ar,fr,es,de',
                    'response_language' => 'string|in:auto,en,ar,fr,es,de',
                    'translation_enabled' => 'boolean'
                ],
                'ui' => [
                    'theme' => 'string|in:light,dark,auto',
                    'compact_mode' => 'boolean',
                    'show_thinking_process' => 'boolean',
                    'show_search_sources' => 'boolean',
                    'animation_enabled' => 'boolean'
                ],
                'behavior' => [
                    'auto_suggest_features' => 'boolean',
                    'remember_conversation_context' => 'boolean',
                    'proactive_assistance' => 'boolean',
                    'learning_mode' => 'boolean'
                ],
                'privacy' => [
                    'save_conversation_history' => 'boolean',
                    'analytics_enabled' => 'boolean',
                    'share_usage_data' => 'boolean',
                    'data_retention_days' => 'integer|min:1|max:365'
                ]
            ]
        ];
    }

    /**
     * Validate preferences against schema
     */
    private function validatePreferences(string $category, array $preferences): void
    {
        $schema = $this->getPreferenceSchema();

        if (!isset($schema['schemas'][$category])) {
            throw new \InvalidArgumentException("No validation schema for category: {$category}");
        }

        $rules = $schema['schemas'][$category];

        $validator = Validator::make($preferences, $rules);

        if ($validator->fails()) {
            throw new \InvalidArgumentException(
                'Preference validation failed: ' . implode(', ', $validator->errors()->all())
            );
        }
    }

    /**
     * Clear user cache for specific category
     */
    private function clearUserCache(string $userIdentifier, string $category): void
    {
        Cache::forget("user_preferences_{$category}_{$userIdentifier}");
        Cache::forget("user_preferences_all_{$userIdentifier}");
    }

    /**
     * Clear all user cache
     */
    private function clearAllUserCache(string $userIdentifier): void
    {
        foreach (array_keys($this->defaultPreferences) as $category) {
            Cache::forget("user_preferences_{$category}_{$userIdentifier}");
        }
        Cache::forget("user_preferences_all_{$userIdentifier}");

        // Also clear feature toggle cache
        $this->featureToggle->clearUserCache($userIdentifier);
    }

    /**
     * Get user preferences summary for analytics
     */
    public function getPreferencesSummary(string $userIdentifier): array
    {
        try {
            $allPrefs = $this->getAllPreferences($userIdentifier);

            // Count enabled features
            $enabledFeatures = array_filter($allPrefs['features'], fn($feature) => $feature['enabled']);

            return [
                'total_features_enabled' => count($enabledFeatures),
                'total_features_available' => count($allPrefs['features']),
                'customized_categories' => array_keys($allPrefs['preferences']),
                'interface_language' => $allPrefs['preferences']['language']['interface_language'] ?? 'en',
                'theme' => $allPrefs['preferences']['ui']['theme'] ?? 'dark',
                'privacy_level' => $this->calculatePrivacyLevel($allPrefs['preferences']['privacy'] ?? [])
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get preferences summary', [
                'user_identifier' => $userIdentifier,
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Calculate privacy level based on privacy settings
     */
    private function calculatePrivacyLevel(array $privacySettings): string
    {
        $score = 0;

        if (!($privacySettings['save_conversation_history'] ?? true)) $score += 2;
        if (!($privacySettings['analytics_enabled'] ?? false)) $score += 1;
        if (!($privacySettings['share_usage_data'] ?? false)) $score += 2;
        if (($privacySettings['data_retention_days'] ?? 30) <= 7) $score += 1;

        if ($score >= 5) return 'high';
        if ($score >= 3) return 'medium';
        return 'low';
    }
}
