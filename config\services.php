<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Free Search Services
    |--------------------------------------------------------------------------
    */
    'free_search' => [
        'duckduckgo' => [
            'base_url' => 'https://api.duckduckgo.com',
            'enabled' => true,
        ],
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'deepseek' => [
        'api_key' => env('DEEPSEEK_API_KEY'),
        'base_url' => env('DEEPSEEK_BASE_URL', 'https://api.deepseek.com'),
        'timeout' => env('DEEPSEEK_TIMEOUT', 10), // Reduced to 10s for faster response
        'max_retries' => env('DEEPSEEK_MAX_RETRIES', 2), // Reduced retries
        'initial_retry_delay' => env('DEEPSEEK_INITIAL_RETRY_DELAY', 500), // Faster retry
        'backoff_factor' => env('DEEPSEEK_BACKOFF_FACTOR', 1.5),
        'failure_threshold' => env('DEEPSEEK_FAILURE_THRESHOLD', 3), // Lower threshold
        'circuit_cooldown' => env('DEEPSEEK_CIRCUIT_COOLDOWN', 15000), // Shorter cooldown
        'enabled' => env('DEEPSEEK_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Unified Search Services Configuration
    |--------------------------------------------------------------------------
    */
    'search' => [
        'default_provider' => env('SEARCH_DEFAULT_PROVIDER', 'duckduckgo'),
        'providers' => [
            'duckduckgo' => [
                'enabled' => env('DUCKDUCKGO_ENABLED', true),
                'base_url' => 'https://api.duckduckgo.com',
                'timeout' => env('DUCKDUCKGO_TIMEOUT', 10),
            ],
            'searx' => [
                'enabled' => env('SEARX_ENABLED', false),
                'base_url' => env('SEARX_BASE_URL', 'https://searx.be/search'),
                'timeout' => env('SEARX_TIMEOUT', 10),
            ],
        ],
        'cache_enabled' => env('SEARCH_CACHE_ENABLED', true),
        'cache_duration' => env('SEARCH_CACHE_DURATION', 15), // minutes
    ],

    /*
    |--------------------------------------------------------------------------
    | Unified Image Generation Services Configuration
    |--------------------------------------------------------------------------
    */
    'image_generation' => [
        'default_provider' => env('IMAGE_DEFAULT_PROVIDER', 'gemini'),
        'providers' => [
            'gemini' => [
                'enabled' => env('GEMINI_IMAGE_ENABLED', true),
                'api_key' => env('GEMINI_API_KEY'),
                'model' => env('GEMINI_IMAGE_MODEL', 'gemini-2.0-flash'),
                'timeout' => env('GEMINI_IMAGE_TIMEOUT', 120),
            ],
            'huggingface' => [
                'enabled' => env('HUGGINGFACE_ENABLED', false),
                'api_key' => env('HUGGINGFACE_API_KEY'),
                'model' => env('HUGGINGFACE_IMAGE_MODEL', 'stabilityai/stable-diffusion-xl-base-1.0'),
                'timeout' => env('HUGGINGFACE_TIMEOUT', 120),
            ],
            'deepseek' => [
                'enabled' => env('DEEPSEEK_IMAGE_ENABLED', false),
                'api_key' => env('DEEPSEEK_API_KEY'),
                'model' => env('DEEPSEEK_IMAGE_MODEL', 'deepseek-vision'),
                'timeout' => env('DEEPSEEK_IMAGE_TIMEOUT', 120),
            ],
        ],
        'free_alternatives' => [
            'enabled' => env('FREE_IMAGE_ALTERNATIVES_ENABLED', true),
            'default_type' => env('FREE_IMAGE_DEFAULT_TYPE', 'description'),
        ],
    ],

    'gemini' => [
        'api_key' => env('GEMINI_API_KEY'),
        'base_url' => env('GEMINI_BASE_URL', 'https://generativelanguage.googleapis.com'),
        'timeout' => env('GEMINI_TIMEOUT', 15), // Reduced to 15s
        'max_retries' => env('GEMINI_MAX_RETRIES', 2), // Reduced retries
        'initial_retry_delay' => env('GEMINI_INITIAL_RETRY_DELAY', 500), // Faster retry
        'backoff_factor' => env('GEMINI_BACKOFF_FACTOR', 1.5),
        'enabled' => env('GEMINI_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | LLM Client Configuration
    |--------------------------------------------------------------------------
    */
    'llm_fallback' => [
        'primary' => env('LLM_PRIMARY', 'deepseek'), // deepseek or gemini
        'fallback_enabled' => env('LLM_FALLBACK_ENABLED', true),
    ],

    'llm_client' => App\Services\ApiFallbackService::class,

];
