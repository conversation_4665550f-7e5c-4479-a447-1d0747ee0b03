<?php

namespace App\Http\Controllers;

use App\Services\UserPreferencesService;
use App\Services\FeatureToggleService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class UserPreferencesController extends Controller
{
    private UserPreferencesService $userPreferences;
    private FeatureToggleService $featureToggle;

    public function __construct(
        UserPreferencesService $userPreferences,
        FeatureToggleService $featureToggle
    ) {
        $this->userPreferences = $userPreferences;
        $this->featureToggle = $featureToggle;
    }

    /**
     * Get all user preferences
     */
    public function getAllPreferences(Request $request): JsonResponse
    {
        try {
            $userIdentifier = $this->getUserIdentifier($request);
            $preferences = $this->userPreferences->getAllPreferences($userIdentifier);

            return response()->json([
                'success' => true,
                'data' => $preferences
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get user preferences', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to retrieve preferences'
            ], 500);
        }
    }

    /**
     * Get preferences for specific category
     */
    public function getCategoryPreferences(Request $request, string $category): JsonResponse
    {
        try {
            $userIdentifier = $this->getUserIdentifier($request);
            $preferences = $this->userPreferences->getCategoryPreferences($userIdentifier, $category);

            return response()->json([
                'success' => true,
                'category' => $category,
                'data' => $preferences
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get category preferences', [
                'category' => $category,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to retrieve category preferences'
            ], 500);
        }
    }

    /**
     * Update preferences for specific category
     */
    public function setCategoryPreferences(Request $request, string $category): JsonResponse
    {
        try {
            $userIdentifier = $this->getUserIdentifier($request);

            $validator = Validator::make($request->all(), [
                'preferences' => 'required|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid request data',
                    'details' => $validator->errors()
                ], 400);
            }

            $preferences = $request->input('preferences');
            $success = $this->userPreferences->setCategoryPreferences($userIdentifier, $category, $preferences);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Preferences updated successfully',
                    'category' => $category
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => 'Failed to update preferences'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('Failed to set category preferences', [
                'category' => $category,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to update preferences'
            ], 500);
        }
    }

    /**
     * Get specific preference value
     */
    public function getPreference(Request $request, string $category, string $key): JsonResponse
    {
        try {
            $userIdentifier = $this->getUserIdentifier($request);
            $value = $this->userPreferences->getPreference($userIdentifier, $category, $key);

            return response()->json([
                'success' => true,
                'category' => $category,
                'key' => $key,
                'value' => $value
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get specific preference', [
                'category' => $category,
                'key' => $key,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to retrieve preference'
            ], 500);
        }
    }

    /**
     * Set specific preference value
     */
    public function setPreference(Request $request, string $category, string $key): JsonResponse
    {
        try {
            $userIdentifier = $this->getUserIdentifier($request);

            $validator = Validator::make($request->all(), [
                'value' => 'required'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid request data',
                    'details' => $validator->errors()
                ], 400);
            }

            $value = $request->input('value');
            $success = $this->userPreferences->setPreference($userIdentifier, $category, $key, $value);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Preference updated successfully',
                    'category' => $category,
                    'key' => $key,
                    'value' => $value
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => 'Failed to update preference'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('Failed to set specific preference', [
                'category' => $category,
                'key' => $key,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to update preference'
            ], 500);
        }
    }

    /**
     * Get all available features
     */
    public function getAvailableFeatures(): JsonResponse
    {
        try {
            $features = $this->featureToggle->getAvailableFeatures();

            return response()->json([
                'success' => true,
                'data' => $features
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get available features', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to retrieve available features'
            ], 500);
        }
    }

    /**
     * Get user's feature status
     */
    public function getUserFeatures(Request $request): JsonResponse
    {
        try {
            $userIdentifier = $this->getUserIdentifier($request);
            $features = $this->featureToggle->getAllUserFeatures($userIdentifier);

            return response()->json([
                'success' => true,
                'data' => $features
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get user features', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to retrieve user features'
            ], 500);
        }
    }

    /**
     * Enable feature for user
     */
    public function enableFeature(Request $request, string $featureKey): JsonResponse
    {
        try {
            $userIdentifier = $this->getUserIdentifier($request);

            $customSettings = $request->input('settings', []);
            $success = $this->featureToggle->enableFeature($userIdentifier, $featureKey, $customSettings);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Feature enabled successfully',
                    'feature' => $featureKey
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => 'Failed to enable feature'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('Failed to enable feature', [
                'feature' => $featureKey,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to enable feature'
            ], 500);
        }
    }

    /**
     * Disable feature for user
     */
    public function disableFeature(Request $request, string $featureKey): JsonResponse
    {
        try {
            $userIdentifier = $this->getUserIdentifier($request);
            $success = $this->featureToggle->disableFeature($userIdentifier, $featureKey);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Feature disabled successfully',
                    'feature' => $featureKey
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => 'Failed to disable feature'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('Failed to disable feature', [
                'feature' => $featureKey,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to disable feature'
            ], 500);
        }
    }

    /**
     * Export user preferences
     */
    public function exportPreferences(Request $request): JsonResponse
    {
        try {
            $userIdentifier = $this->getUserIdentifier($request);
            $exportData = $this->userPreferences->exportPreferences($userIdentifier);

            return response()->json([
                'success' => true,
                'data' => $exportData
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to export preferences', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to export preferences'
            ], 500);
        }
    }

    /**
     * Import user preferences
     */
    public function importPreferences(Request $request): JsonResponse
    {
        try {
            $userIdentifier = $this->getUserIdentifier($request);

            $validator = Validator::make($request->all(), [
                'export_data' => 'required|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid import data',
                    'details' => $validator->errors()
                ], 400);
            }

            $exportData = $request->input('export_data');
            $success = $this->userPreferences->importPreferences($userIdentifier, $exportData);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Preferences imported successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => 'Failed to import preferences'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('Failed to import preferences', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to import preferences'
            ], 500);
        }
    }

    /**
     * Reset preferences to defaults
     */
    public function resetToDefaults(Request $request): JsonResponse
    {
        try {
            $userIdentifier = $this->getUserIdentifier($request);
            $success = $this->userPreferences->resetToDefaults($userIdentifier);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Preferences reset to defaults successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => 'Failed to reset preferences'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('Failed to reset preferences', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to reset preferences'
            ], 500);
        }
    }

    /**
     * Get preference schema
     */
    public function getPreferenceSchema(): JsonResponse
    {
        try {
            $schema = $this->userPreferences->getPreferenceSchema();

            return response()->json([
                'success' => true,
                'data' => $schema
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get preference schema', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to retrieve preference schema'
            ], 500);
        }
    }

    /**
     * Get user identifier from request
     */
    private function getUserIdentifier(Request $request): string
    {
        // Try to get from header first (for API calls)
        $sessionId = $request->header('X-Session-ID');

        if (!empty($sessionId)) {
            return $sessionId;
        }

        // Try to get from session if available
        try {
            $sessionId = $request->session()->getId();
            if (!empty($sessionId)) {
                return $sessionId;
            }
        } catch (\Exception $e) {
            // Session not available, continue to fallback
        }

        // Fallback: generate a default identifier
        return 'default_user';
    }
}
