<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserPreference extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_identifier',
        'preference_key',
        'preference_value',
        'is_enabled',
        'description'
    ];

    protected $casts = [
        'preference_value' => 'array',
        'is_enabled' => 'boolean'
    ];

    /**
     * Get user preference by key
     */
    public static function getUserPreference(string $userIdentifier, string $key, $default = null)
    {
        $preference = self::where('user_identifier', $userIdentifier)
            ->where('preference_key', $key)
            ->first();

        return $preference ? $preference->preference_value : $default;
    }

    /**
     * Set user preference
     */
    public static function setUserPreference(string $userIdentifier, string $key, $value, bool $isEnabled = true, string $description = null)
    {
        return self::updateOrCreate(
            [
                'user_identifier' => $userIdentifier,
                'preference_key' => $key
            ],
            [
                'preference_value' => $value,
                'is_enabled' => $isEnabled,
                'description' => $description
            ]
        );
    }

    /**
     * Check if feature is enabled for user
     */
    public static function isFeatureEnabled(string $userIdentifier, string $featureKey): bool
    {
        $preference = self::where('user_identifier', $userIdentifier)
            ->where('preference_key', $featureKey)
            ->first();

        return $preference ? $preference->is_enabled : false;
    }

    /**
     * Get all user preferences
     */
    public static function getAllUserPreferences(string $userIdentifier): array
    {
        return self::where('user_identifier', $userIdentifier)
            ->get()
            ->keyBy('preference_key')
            ->map(function ($preference) {
                return [
                    'value' => $preference->preference_value,
                    'enabled' => $preference->is_enabled,
                    'description' => $preference->description
                ];
            })
            ->toArray();
    }
}
