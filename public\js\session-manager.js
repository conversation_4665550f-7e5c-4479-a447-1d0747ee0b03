/**
 * WIDDX AI - Session Management
 * Handles chat sessions, history, and persistence
 */

class SessionManager {
    constructor() {
        this.sessions = new Map();
        this.currentSessionId = null;
        this.maxSessions = 50;
        this.maxMessagesPerSession = 100;
        
        this.loadSessions();
    }
    
    // Create a new session
    createSession(title = null) {
        const sessionId = this.generateSessionId();
        const session = {
            id: sessionId,
            title: title || `Chat ${new Date().toLocaleDateString()}`,
            messages: [],
            features: [],
            language: 'en',
            createdAt: Date.now(),
            updatedAt: Date.now()
        };
        
        this.sessions.set(sessionId, session);
        this.currentSessionId = sessionId;
        this.saveSessions();
        
        console.log(`Created new session: ${sessionId}`);
        return session;
    }
    
    // Get current session or create new one
    getCurrentSession() {
        if (!this.currentSessionId || !this.sessions.has(this.currentSessionId)) {
            return this.createSession();
        }
        return this.sessions.get(this.currentSessionId);
    }
    
    // Switch to a specific session
    switchToSession(sessionId) {
        if (this.sessions.has(sessionId)) {
            this.currentSessionId = sessionId;
            this.saveSessions();
            return this.sessions.get(sessionId);
        }
        return null;
    }
    
    // Add message to current session
    addMessage(content, sender, metadata = {}) {
        const session = this.getCurrentSession();
        const message = {
            id: this.generateMessageId(),
            content,
            sender,
            metadata,
            timestamp: Date.now()
        };
        
        session.messages.push(message);
        session.updatedAt = Date.now();
        
        // Update session title if it's the first user message
        if (sender === 'user' && session.messages.filter(m => m.sender === 'user').length === 1) {
            session.title = this.generateSessionTitle(content);
        }
        
        // Limit messages per session
        if (session.messages.length > this.maxMessagesPerSession) {
            session.messages = session.messages.slice(-this.maxMessagesPerSession);
        }
        
        this.saveSessions();
        return message;
    }
    
    // Get session history
    getSessionHistory(sessionId = null) {
        const session = sessionId ? this.sessions.get(sessionId) : this.getCurrentSession();
        return session ? session.messages : [];
    }
    
    // Get all sessions sorted by update time
    getAllSessions() {
        return Array.from(this.sessions.values())
            .sort((a, b) => b.updatedAt - a.updatedAt);
    }
    
    // Delete a session
    deleteSession(sessionId) {
        if (this.sessions.has(sessionId)) {
            this.sessions.delete(sessionId);
            
            // If deleting current session, switch to most recent
            if (this.currentSessionId === sessionId) {
                const allSessions = this.getAllSessions();
                this.currentSessionId = allSessions.length > 0 ? allSessions[0].id : null;
            }
            
            this.saveSessions();
            return true;
        }
        return false;
    }
    
    // Update session features
    updateSessionFeatures(features) {
        const session = this.getCurrentSession();
        session.features = Array.from(features);
        session.updatedAt = Date.now();
        this.saveSessions();
    }
    
    // Update session language
    updateSessionLanguage(language) {
        const session = this.getCurrentSession();
        session.language = language;
        session.updatedAt = Date.now();
        this.saveSessions();
    }
    
    // Search sessions
    searchSessions(query) {
        const lowercaseQuery = query.toLowerCase();
        return this.getAllSessions().filter(session => {
            // Search in title
            if (session.title.toLowerCase().includes(lowercaseQuery)) {
                return true;
            }
            
            // Search in messages
            return session.messages.some(message => 
                message.content.toLowerCase().includes(lowercaseQuery)
            );
        });
    }
    
    // Export session data
    exportSession(sessionId) {
        const session = this.sessions.get(sessionId);
        if (!session) return null;
        
        return {
            ...session,
            exportedAt: Date.now(),
            version: '1.0'
        };
    }
    
    // Import session data
    importSession(sessionData) {
        if (!sessionData || !sessionData.id) return false;
        
        // Generate new ID to avoid conflicts
        const newId = this.generateSessionId();
        const importedSession = {
            ...sessionData,
            id: newId,
            title: `${sessionData.title} (Imported)`,
            importedAt: Date.now()
        };
        
        this.sessions.set(newId, importedSession);
        this.saveSessions();
        return newId;
    }
    
    // Generate session title from first message
    generateSessionTitle(firstMessage) {
        const maxLength = 50;
        let title = firstMessage.trim();
        
        // Remove common prefixes
        title = title.replace(/^(what|how|why|when|where|who|can|could|would|should|is|are|do|does|did)\s+/i, '');
        
        // Truncate if too long
        if (title.length > maxLength) {
            title = title.substring(0, maxLength).trim() + '...';
        }
        
        return title || 'New Chat';
    }
    
    // Generate unique session ID
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    // Generate unique message ID
    generateMessageId() {
        return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    // Save sessions to localStorage
    saveSessions() {
        try {
            const sessionsData = {
                sessions: Object.fromEntries(this.sessions),
                currentSessionId: this.currentSessionId,
                lastSaved: Date.now()
            };
            
            localStorage.setItem('widdx-sessions', JSON.stringify(sessionsData));
            
            // Also save current session ID separately for quick access
            if (this.currentSessionId) {
                localStorage.setItem('widdx-current-session', this.currentSessionId);
            }
        } catch (error) {
            console.warn('Failed to save sessions:', error);
        }
    }
    
    // Load sessions from localStorage
    loadSessions() {
        try {
            const sessionsData = localStorage.getItem('widdx-sessions');
            if (sessionsData) {
                const data = JSON.parse(sessionsData);
                
                // Convert back to Map
                this.sessions = new Map(Object.entries(data.sessions || {}));
                this.currentSessionId = data.currentSessionId;
                
                // Clean up old sessions
                this.cleanupOldSessions();
            }
            
            // Load current session ID if not set
            if (!this.currentSessionId) {
                this.currentSessionId = localStorage.getItem('widdx-current-session');
            }
            
        } catch (error) {
            console.warn('Failed to load sessions:', error);
            this.sessions = new Map();
            this.currentSessionId = null;
        }
    }
    
    // Clean up old sessions to prevent storage bloat
    cleanupOldSessions() {
        const allSessions = this.getAllSessions();
        
        // Keep only the most recent sessions
        if (allSessions.length > this.maxSessions) {
            const sessionsToKeep = allSessions.slice(0, this.maxSessions);
            const keepIds = new Set(sessionsToKeep.map(s => s.id));
            
            // Remove old sessions
            for (const [sessionId] of this.sessions) {
                if (!keepIds.has(sessionId)) {
                    this.sessions.delete(sessionId);
                }
            }
        }
        
        // Remove sessions older than 30 days
        const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
        for (const [sessionId, session] of this.sessions) {
            if (session.updatedAt < thirtyDaysAgo) {
                this.sessions.delete(sessionId);
            }
        }
    }
    
    // Get session statistics
    getStatistics() {
        const allSessions = this.getAllSessions();
        const totalMessages = allSessions.reduce((sum, session) => sum + session.messages.length, 0);
        const languages = [...new Set(allSessions.map(s => s.language))];
        
        return {
            totalSessions: allSessions.length,
            totalMessages,
            languages,
            oldestSession: allSessions.length > 0 ? Math.min(...allSessions.map(s => s.createdAt)) : null,
            newestSession: allSessions.length > 0 ? Math.max(...allSessions.map(s => s.createdAt)) : null
        };
    }
    
    // Clear all sessions
    clearAllSessions() {
        this.sessions.clear();
        this.currentSessionId = null;
        localStorage.removeItem('widdx-sessions');
        localStorage.removeItem('widdx-current-session');
        console.log('All sessions cleared');
    }
}

// Export for global access
window.SessionManager = SessionManager;
