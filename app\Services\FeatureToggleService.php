<?php

namespace App\Services;

use App\Models\UserPreference;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class FeatureToggleService
{
    // Available features
    public const FEATURE_DEEP_THINKING = 'deep_thinking';
    public const FEATURE_DEEP_SEARCH = 'deep_search';
    public const FEATURE_ULTRA_DEEP_SEARCH = 'ultra_deep_search';
    public const FEATURE_LIVE_SEARCH = 'live_search';
    public const FEATURE_ADVANCED_PERSONALITY = 'advanced_personality';
    public const FEATURE_IMAGE_GENERATION = 'image_generation';
    public const FEATURE_VOICE_MODE = 'voice_mode';
    public const FEATURE_DOCUMENT_ANALYSIS = 'document_analysis';
    public const FEATURE_VISION_ANALYSIS = 'vision_analysis';

    // Default feature settings
    private array $defaultFeatures = [
        self::FEATURE_DEEP_THINKING => [
            'enabled' => false,
            'settings' => [
                'max_thinking_depth' => 3,
                'show_thinking_process' => true,
                'thinking_timeout_seconds' => 60
            ],
            'description' => 'Advanced multi-stage thinking and reasoning'
        ],
        self::FEATURE_DEEP_SEARCH => [
            'enabled' => false,
            'settings' => [
                'max_sources' => 5,
                'search_timeout_seconds' => 30,
                'include_synthesis' => true
            ],
            'description' => 'Deep internet search with multiple sources'
        ],
        self::FEATURE_ULTRA_DEEP_SEARCH => [
            'enabled' => false,
            'settings' => [
                'max_sources' => 10,
                'search_timeout_seconds' => 60,
                'include_analysis' => true,
                'cross_reference' => true
            ],
            'description' => 'Ultra-deep search with comprehensive analysis'
        ],
        self::FEATURE_LIVE_SEARCH => [
            'enabled' => true,
            'settings' => [
                'max_results' => 5,
                'cache_duration_minutes' => 15
            ],
            'description' => 'Real-time internet search capabilities'
        ],
        self::FEATURE_ADVANCED_PERSONALITY => [
            'enabled' => true,
            'settings' => [
                'personality_adaptation' => true,
                'context_awareness' => true
            ],
            'description' => 'Advanced personality and context awareness'
        ],
        self::FEATURE_IMAGE_GENERATION => [
            'enabled' => true,
            'settings' => [
                'max_images_per_request' => 1,
                'default_style' => 'realistic'
            ],
            'description' => 'AI-powered image generation'
        ],
        self::FEATURE_VOICE_MODE => [
            'enabled' => false,
            'settings' => [
                'voice_speed' => 'normal',
                'voice_language' => 'auto'
            ],
            'description' => 'Voice interaction capabilities'
        ],
        self::FEATURE_DOCUMENT_ANALYSIS => [
            'enabled' => false,
            'settings' => [
                'max_document_size_mb' => 10,
                'supported_formats' => ['pdf', 'docx', 'txt']
            ],
            'description' => 'Document analysis and processing'
        ],
        self::FEATURE_VISION_ANALYSIS => [
            'enabled' => false,
            'settings' => [
                'max_image_size_mb' => 5,
                'supported_formats' => ['jpg', 'png', 'webp']
            ],
            'description' => 'Image and visual content analysis'
        ]
    ];

    private int $cacheMinutes = 30;

    /**
     * Check if feature is enabled for user
     */
    public function isFeatureEnabled(string $userIdentifier, string $featureKey): bool
    {
        $cacheKey = "feature_toggle_{$userIdentifier}_{$featureKey}";
        
        return Cache::remember($cacheKey, now()->addMinutes($this->cacheMinutes), function () use ($userIdentifier, $featureKey) {
            return UserPreference::isFeatureEnabled($userIdentifier, $featureKey);
        });
    }

    /**
     * Enable feature for user
     */
    public function enableFeature(string $userIdentifier, string $featureKey, array $customSettings = []): bool
    {
        try {
            if (!$this->isValidFeature($featureKey)) {
                throw new \InvalidArgumentException("Invalid feature key: {$featureKey}");
            }

            $defaultSettings = $this->defaultFeatures[$featureKey]['settings'] ?? [];
            $settings = array_merge($defaultSettings, $customSettings);

            UserPreference::setUserPreference(
                $userIdentifier,
                $featureKey,
                $settings,
                true,
                $this->defaultFeatures[$featureKey]['description'] ?? ''
            );

            // Clear cache
            $this->clearFeatureCache($userIdentifier, $featureKey);

            Log::info('Feature enabled for user', [
                'user_identifier' => $userIdentifier,
                'feature' => $featureKey,
                'settings' => $settings
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to enable feature', [
                'user_identifier' => $userIdentifier,
                'feature' => $featureKey,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Disable feature for user
     */
    public function disableFeature(string $userIdentifier, string $featureKey): bool
    {
        try {
            if (!$this->isValidFeature($featureKey)) {
                throw new \InvalidArgumentException("Invalid feature key: {$featureKey}");
            }

            UserPreference::setUserPreference(
                $userIdentifier,
                $featureKey,
                $this->defaultFeatures[$featureKey]['settings'] ?? [],
                false,
                $this->defaultFeatures[$featureKey]['description'] ?? ''
            );

            // Clear cache
            $this->clearFeatureCache($userIdentifier, $featureKey);

            Log::info('Feature disabled for user', [
                'user_identifier' => $userIdentifier,
                'feature' => $featureKey
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to disable feature', [
                'user_identifier' => $userIdentifier,
                'feature' => $featureKey,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Get feature settings for user
     */
    public function getFeatureSettings(string $userIdentifier, string $featureKey): array
    {
        $cacheKey = "feature_settings_{$userIdentifier}_{$featureKey}";
        
        return Cache::remember($cacheKey, now()->addMinutes($this->cacheMinutes), function () use ($userIdentifier, $featureKey) {
            $userSettings = UserPreference::getUserPreference($userIdentifier, $featureKey);
            $defaultSettings = $this->defaultFeatures[$featureKey]['settings'] ?? [];
            
            return is_array($userSettings) ? array_merge($defaultSettings, $userSettings) : $defaultSettings;
        });
    }

    /**
     * Get all user features status
     */
    public function getAllUserFeatures(string $userIdentifier): array
    {
        $cacheKey = "all_features_{$userIdentifier}";
        
        return Cache::remember($cacheKey, now()->addMinutes($this->cacheMinutes), function () use ($userIdentifier) {
            $userPreferences = UserPreference::getAllUserPreferences($userIdentifier);
            $result = [];

            foreach ($this->defaultFeatures as $featureKey => $defaultConfig) {
                $userConfig = $userPreferences[$featureKey] ?? null;
                
                $result[$featureKey] = [
                    'enabled' => $userConfig['enabled'] ?? $defaultConfig['enabled'],
                    'settings' => is_array($userConfig['value'] ?? null) 
                        ? array_merge($defaultConfig['settings'], $userConfig['value'])
                        : $defaultConfig['settings'],
                    'description' => $defaultConfig['description']
                ];
            }

            return $result;
        });
    }

    /**
     * Get available features list
     */
    public function getAvailableFeatures(): array
    {
        return array_map(function ($config, $key) {
            return [
                'key' => $key,
                'description' => $config['description'],
                'default_enabled' => $config['enabled'],
                'settings_schema' => $config['settings']
            ];
        }, $this->defaultFeatures, array_keys($this->defaultFeatures));
    }

    /**
     * Initialize default features for new user
     */
    public function initializeUserFeatures(string $userIdentifier): void
    {
        foreach ($this->defaultFeatures as $featureKey => $config) {
            if (!UserPreference::where('user_identifier', $userIdentifier)
                ->where('preference_key', $featureKey)
                ->exists()) {
                
                UserPreference::setUserPreference(
                    $userIdentifier,
                    $featureKey,
                    $config['settings'],
                    $config['enabled'],
                    $config['description']
                );
            }
        }

        Log::info('Default features initialized for user', [
            'user_identifier' => $userIdentifier
        ]);
    }

    /**
     * Check if feature key is valid
     */
    private function isValidFeature(string $featureKey): bool
    {
        return array_key_exists($featureKey, $this->defaultFeatures);
    }

    /**
     * Clear feature cache for user
     */
    private function clearFeatureCache(string $userIdentifier, string $featureKey): void
    {
        Cache::forget("feature_toggle_{$userIdentifier}_{$featureKey}");
        Cache::forget("feature_settings_{$userIdentifier}_{$featureKey}");
        Cache::forget("all_features_{$userIdentifier}");
    }

    /**
     * Clear all cache for user
     */
    public function clearUserCache(string $userIdentifier): void
    {
        foreach (array_keys($this->defaultFeatures) as $featureKey) {
            $this->clearFeatureCache($userIdentifier, $featureKey);
        }
    }
}
