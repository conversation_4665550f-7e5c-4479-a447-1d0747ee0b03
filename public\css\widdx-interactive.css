/* WIDDX AI - Interactive Enhancements */

/* ===== ADVANCED ANIMATIONS ===== */
@keyframes widdx-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(255, 107, 53, 0.3); }
  50% { box-shadow: 0 0 40px rgba(255, 107, 53, 0.6); }
}

@keyframes widdx-pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.8; }
}

@keyframes widdx-slide-in {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes widdx-bounce-in {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes widdx-shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

@keyframes widdx-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* ===== ENHANCED FEATURE TOGGLES ===== */
.widdx-feature-toggle {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  background: var(--bg-elevated);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-full);
  color: var(--text-secondary);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  user-select: none;
  overflow: hidden;
}

.widdx-feature-toggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 107, 53, 0.2), transparent);
  transition: left 0.5s ease;
}

.widdx-feature-toggle:hover::before {
  left: 100%;
}

.widdx-feature-toggle:hover {
  background: var(--bg-hover);
  border-color: var(--border-secondary);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.widdx-feature-toggle.active {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  border-color: var(--accent-primary);
  color: white;
  animation: widdx-glow 2s ease-in-out infinite;
}

.widdx-feature-toggle.active:hover {
  transform: translateY(-2px) scale(1.02);
}

.widdx-feature-toggle.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.widdx-toggle-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform var(--transition-fast);
}

.widdx-feature-toggle.active .widdx-toggle-icon {
  transform: scale(1.1);
}

.widdx-toggle-indicator {
  width: 8px;
  height: 8px;
  background: var(--success);
  border-radius: var(--radius-full);
  position: absolute;
  top: 4px;
  right: 4px;
  opacity: 0;
  transform: scale(0);
  transition: all var(--transition-fast);
}

.widdx-feature-toggle.active .widdx-toggle-indicator {
  opacity: 1;
  transform: scale(1);
  animation: widdx-pulse 1.5s ease-in-out infinite;
}

/* ===== ENHANCED SIDEBAR ===== */
.widdx-sidebar {
  background: linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
}

.widdx-nav-item {
  position: relative;
  overflow: hidden;
}

.widdx-nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 107, 53, 0.1), transparent);
  transition: left 0.3s ease;
}

.widdx-nav-item:hover::before {
  left: 100%;
}

.widdx-nav-item.active::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--accent-primary);
  animation: widdx-glow 2s ease-in-out infinite;
}

/* ===== ENHANCED MESSAGES ===== */
.widdx-message {
  animation: widdx-slide-in 0.4s ease-out forwards;
}

.widdx-message-bubble {
  position: relative;
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
}

.widdx-message-bubble:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.widdx-message.user .widdx-message-bubble {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.widdx-message.assistant .widdx-message-bubble {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
}

/* ===== ENHANCED INPUT AREA ===== */
.widdx-input-container {
  position: relative;
}

.widdx-textarea {
  background: var(--bg-elevated);
  border: 2px solid var(--border-primary);
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
}

.widdx-textarea:focus {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 4px rgba(255, 107, 53, 0.2);
  transform: translateY(-2px);
}

.widdx-send-btn {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
  transition: all var(--transition-normal);
}

.widdx-send-btn:hover:not(:disabled) {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.6);
}

.widdx-send-btn:active {
  transform: translateY(-1px) scale(0.98);
}

/* ===== THINKING INDICATOR ENHANCEMENTS ===== */
.widdx-thinking {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  backdrop-filter: blur(10px);
  animation: widdx-slide-in 0.4s ease-out forwards;
}

.widdx-thinking-step {
  transition: all var(--transition-normal);
  animation: widdx-slide-in 0.3s ease-out forwards;
}

.widdx-thinking-step.active {
  background: rgba(255, 107, 53, 0.1);
  border-left: 3px solid var(--accent-primary);
  transform: translateX(5px);
}

.widdx-thinking-step.completed {
  background: rgba(76, 175, 80, 0.1);
  border-left: 3px solid var(--success);
}

/* ===== WELCOME SCREEN ENHANCEMENTS ===== */
.widdx-welcome {
  animation: widdx-slide-in 0.6s ease-out forwards;
}

.widdx-welcome-title {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: widdx-glow 3s ease-in-out infinite;
}

.widdx-feature-card {
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.widdx-feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 107, 53, 0.1), transparent);
  transition: left 0.5s ease;
}

.widdx-feature-card:hover::before {
  left: 100%;
}

.widdx-feature-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
  border-color: var(--accent-primary);
}

.widdx-feature-card.active {
  border-color: var(--accent-primary);
  background: rgba(255, 107, 53, 0.05);
}

.widdx-feature-icon {
  transition: all var(--transition-normal);
}

.widdx-feature-card:hover .widdx-feature-icon {
  transform: scale(1.1) rotate(5deg);
  animation: widdx-float 2s ease-in-out infinite;
}

/* ===== NOTIFICATION SYSTEM ===== */
.widdx-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-md) var(--space-lg);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
  z-index: var(--z-tooltip);
  animation: widdx-slide-in 0.3s ease-out forwards;
  backdrop-filter: blur(10px);
}

.widdx-notification.success {
  border-color: var(--success);
  background: rgba(76, 175, 80, 0.1);
}

.widdx-notification.error {
  border-color: var(--error);
  background: rgba(244, 67, 54, 0.1);
}

.widdx-notification.warning {
  border-color: var(--warning);
  background: rgba(255, 152, 0, 0.1);
}

/* ===== LOADING STATES ===== */
.widdx-loading {
  position: relative;
  overflow: hidden;
}

.widdx-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 107, 53, 0.3), transparent);
  animation: widdx-loading-shimmer 1.5s infinite;
}

@keyframes widdx-loading-shimmer {
  to { left: 100%; }
}

/* ===== ENHANCED SCROLLBAR ===== */
.widdx-chat-area::-webkit-scrollbar {
  width: 8px;
}

.widdx-chat-area::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
}

.widdx-chat-area::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--accent-primary), var(--accent-secondary));
  border-radius: var(--radius-full);
  transition: background var(--transition-fast);
}

.widdx-chat-area::-webkit-scrollbar-thumb:hover {
  background: var(--accent-hover);
}

/* ===== MOBILE ENHANCEMENTS ===== */
@media (max-width: 768px) {
  .widdx-sidebar {
    transform: translateX(-100%);
  }
  
  .widdx-sidebar.open {
    transform: translateX(0);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  }
  
  .widdx-feature-toggle {
    padding: var(--space-xs) var(--space-sm);
    font-size: 12px;
  }
  
  .widdx-feature-card:hover {
    transform: translateY(-3px) scale(1.01);
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  .widdx-feature-toggle,
  .widdx-message-bubble,
  .widdx-nav-item {
    border-width: 2px !important;
  }
}
