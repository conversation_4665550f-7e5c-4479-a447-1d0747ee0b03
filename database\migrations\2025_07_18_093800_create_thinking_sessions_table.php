<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('thinking_sessions', function (Blueprint $table) {
            $table->id();
            $table->string('session_id')->index();
            $table->string('thinking_mode')->index(); // 'deep', 'analytical', 'creative'
            $table->text('original_question');
            $table->json('thinking_steps'); // Array of thinking steps
            $table->longText('thought_process'); // Detailed thought process
            $table->longText('final_conclusion');
            $table->integer('thinking_depth_level')->default(1); // 1-5 depth levels
            $table->integer('processing_time_ms')->default(0);
            $table->integer('steps_count')->default(0);
            $table->float('confidence_score')->default(0.0);
            $table->json('sources_consulted')->nullable(); // External sources if any
            $table->boolean('was_successful')->default(false);
            $table->text('error_message')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('thinking_sessions');
    }
};
