/* WIDDX AI Enhanced Design System - Calm & Elegant */

/* ===== CORE ANIMATIONS ===== */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes gentlePulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes smoothGlow {
    0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.2); }
    50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.4); }
}

/* Animation Classes */
.animate-fade-in {
    animation: fadeIn 0.4s ease-out forwards;
}

.animate-slide-in-right {
    animation: slideInRight 0.4s ease-out forwards;
}

.animate-slide-in-up {
    animation: slideInUp 0.4s ease-out forwards;
}

.animate-fade-in-up {
    animation: fadeInUp 0.4s ease-out forwards;
}

.animate-gentle-pulse {
    animation: gentlePulse 2s ease-in-out infinite;
}

.animate-smooth-glow {
    animation: smoothGlow 3s ease-in-out infinite;
}

/* ===== ENHANCED ROOT VARIABLES ===== */
:root {
  /* ===== CALM & ELEGANT COLOR SYSTEM ===== */
  /* Primary Colors - Refined Blue Scale (Calmer) */
  --primary-50: #f8fafc;
  --primary-100: #f1f5f9;
  --primary-200: #e2e8f0;
  --primary-300: #cbd5e1;
  --primary-400: #94a3b8;
  --primary-500: #64748b;
  --primary-600: #475569;
  --primary-700: #334155;
  --primary-800: #1e293b;
  --primary-900: #0f172a;
  --primary-950: #020617;

  /* Accent Colors - Subtle Blue (Main Brand) */
  --accent-50: #eff6ff;
  --accent-100: #dbeafe;
  --accent-200: #bfdbfe;
  --accent-300: #93c5fd;
  --accent-400: #60a5fa;
  --accent-500: #3b82f6;
  --accent-600: #2563eb;
  --accent-700: #1d4ed8;
  --accent-800: #1e40af;
  --accent-900: #1e3a8a;
  --accent-950: #172554;

  /* ===== CALM NEUTRAL SYSTEM ===== */
  /* Warm Neutrals for Better Eye Comfort */
  --neutral-50: #fafaf9;
  --neutral-100: #f5f5f4;
  --neutral-200: #e7e5e4;
  --neutral-300: #d6d3d1;
  --neutral-400: #a8a29e;
  --neutral-500: #78716c;
  --neutral-600: #57534e;
  --neutral-700: #44403c;
  --neutral-800: #292524;
  --neutral-900: #1c1917;
  --neutral-950: #0c0a09;

  /* ===== SEMANTIC COLORS ===== */
  /* Success Colors */
  --success-50: #ecfdf5;
  --success-100: #d1fae5;
  --success-500: #10b981;
  --success-600: #059669;
  --success-700: #047857;
  --success-900: #064e3b;

  /* Warning Colors */
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;
  --warning-900: #78350f;

  /* Error Colors */
  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --error-700: #b91c1c;
  --error-900: #7f1d1d;

  /* Info Colors */
  --info-50: #ecfeff;
  --info-100: #cffafe;
  --info-500: #06b6d4;
  --info-600: #0891b2;
  --info-700: #0e7490;
  --info-900: #164e63;

  /* ===== CALM THEME VARIABLES (Default: Dark) ===== */
  /* Background Colors - Warmer and Softer */
  --bg-primary: var(--neutral-950);
  --bg-secondary: var(--neutral-900);
  --bg-tertiary: var(--neutral-800);
  --bg-elevated: var(--neutral-800);
  --bg-hover: var(--neutral-700);
  --bg-overlay: rgba(28, 25, 23, 0.85);
  --bg-glass: rgba(250, 250, 249, 0.03);
  --bg-card: var(--neutral-850);
  --bg-input: var(--neutral-800);

  /* Text Colors - Better Contrast and Readability */
  --text-primary: var(--neutral-50);
  --text-secondary: var(--neutral-300);
  --text-tertiary: var(--neutral-400);
  --text-muted: var(--neutral-500);
  --text-inverse: var(--neutral-900);
  --text-accent: var(--accent-400);

  /* Border Colors - Softer and More Subtle */
  --border-primary: var(--neutral-700);
  --border-secondary: var(--neutral-600);
  --border-focus: var(--accent-500);
  --border-error: var(--error-500);
  --border-success: var(--success-500);
  --border-subtle: var(--neutral-800);

  /* ===== TYPOGRAPHY SYSTEM ===== */
  /* Font Families */
  --font-sans: 'Inter', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, 'Liberation Mono', 'Menlo', monospace;

  /* Font Sizes with Line Heights */
  --text-xs: 0.75rem;     /* 12px */
  --text-sm: 0.875rem;    /* 14px */
  --text-base: 1rem;      /* 16px */
  --text-lg: 1.125rem;    /* 18px */
  --text-xl: 1.25rem;     /* 20px */
  --text-2xl: 1.5rem;     /* 24px */
  --text-3xl: 1.875rem;   /* 30px */
  --text-4xl: 2.25rem;    /* 36px */
  --text-5xl: 3rem;       /* 48px */

  /* Line Heights */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* Font Weights */
  --font-thin: 100;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;

  /* ===== SPACING SYSTEM ===== */
  --space-0: 0;
  --space-px: 1px;
  --space-0-5: 0.125rem;  /* 2px */
  --space-1: 0.25rem;     /* 4px */
  --space-1-5: 0.375rem;  /* 6px */
  --space-2: 0.5rem;      /* 8px */
  --space-2-5: 0.625rem;  /* 10px */
  --space-3: 0.75rem;     /* 12px */
  --space-3-5: 0.875rem;  /* 14px */
  --space-4: 1rem;        /* 16px */
  --space-5: 1.25rem;     /* 20px */
  --space-6: 1.5rem;      /* 24px */
  --space-7: 1.75rem;     /* 28px */
  --space-8: 2rem;        /* 32px */
  --space-9: 2.25rem;     /* 36px */
  --space-10: 2.5rem;     /* 40px */
  --space-12: 3rem;       /* 48px */
  --space-14: 3.5rem;     /* 56px */
  --space-16: 4rem;       /* 64px */
  --space-20: 5rem;       /* 80px */
  --space-24: 6rem;       /* 96px */
  --space-32: 8rem;       /* 128px */

  /* ===== BORDER RADIUS SYSTEM ===== */
  --radius-none: 0;
  --radius-sm: 0.125rem;   /* 2px */
  --radius: 0.25rem;       /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-3xl: 1.5rem;    /* 24px */
  --radius-full: 9999px;

  /* ===== SHADOW SYSTEM ===== */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px -1px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -2px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -4px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 8px 10px -6px rgba(0, 0, 0, 0.6);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.7);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.3);
  --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
  --shadow-glow-lg: 0 0 40px rgba(59, 130, 246, 0.4);

  /* ===== TRANSITION SYSTEM ===== */
  --transition-none: none;
  --transition-all: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: all 250ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 350ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: all 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* ===== Z-INDEX SYSTEM ===== */
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;

  /* ===== RESPONSIVE BREAKPOINTS ===== */
  --breakpoint-xs: 475px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* ===== CONTAINER SIZES ===== */
  --container-xs: 20rem;    /* 320px */
  --container-sm: 24rem;    /* 384px */
  --container-md: 28rem;    /* 448px */
  --container-lg: 32rem;    /* 512px */
  --container-xl: 36rem;    /* 576px */
  --container-2xl: 42rem;   /* 672px */
  --container-3xl: 48rem;   /* 768px */
  --container-4xl: 56rem;   /* 896px */
  --container-5xl: 64rem;   /* 1024px */
  --container-6xl: 72rem;   /* 1152px */
  --container-7xl: 80rem;   /* 1280px */

  /* ===== LEGACY COMPATIBILITY ===== */
  /* Maintain backward compatibility with existing variables */
  --widdx-primary: var(--primary-700);
  --widdx-primary-hover: var(--primary-800);
  --widdx-primary-light: var(--primary-500);
  --widdx-secondary: var(--secondary-700);
  --widdx-secondary-hover: var(--secondary-800);
  --widdx-bg-primary: var(--bg-primary);
  --widdx-bg-secondary: var(--bg-secondary);
  --widdx-bg-tertiary: var(--bg-tertiary);
  --widdx-bg-elevated: var(--bg-elevated);
  --widdx-bg-hover: var(--bg-hover);
  --widdx-text-primary: var(--text-primary);
  --widdx-text-secondary: var(--text-secondary);
  --widdx-text-tertiary: var(--text-tertiary);
  --widdx-text-muted: var(--text-muted);
  --widdx-border-primary: var(--border-primary);
  --widdx-border-secondary: var(--border-secondary);
  --widdx-border-focus: var(--border-focus);
  --widdx-success: var(--success-500);
  --widdx-warning: var(--warning-500);
  --widdx-error: var(--error-500);
  --widdx-info: var(--info-500);
  --widdx-font-family: var(--font-sans);
  --widdx-font-mono: var(--font-mono);
  --widdx-transition-fast: var(--transition-fast);
  --widdx-transition-normal: var(--transition-normal);
  --widdx-transition-slow: var(--transition-slow);

  /* Alert Colors for Dark Theme (Default) */
  --widdx-alert-bg: var(--bg-elevated);
  --widdx-alert-text: var(--text-primary);
  --widdx-alert-border: var(--border-primary);

  --widdx-alert-success-bg: rgba(16, 185, 129, 0.1);
  --widdx-alert-success-text: var(--success-500);
  --widdx-alert-success-border: var(--success-500);

  --widdx-alert-error-bg: rgba(239, 68, 68, 0.1);
  --widdx-alert-error-text: var(--error-500);
  --widdx-alert-error-border: var(--error-500);

  --widdx-alert-warning-bg: rgba(245, 158, 11, 0.1);
  --widdx-alert-warning-text: var(--warning-500);
  --widdx-alert-warning-border: var(--warning-500);

  --widdx-alert-info-bg: rgba(6, 182, 212, 0.1);
  --widdx-alert-info-text: var(--info-500);
  --widdx-alert-info-border: var(--info-500);
}

/* ===== LIGHT THEME SUPPORT ===== */
[data-theme="light"] {
  /* Background Colors */
  --bg-primary: var(--neutral-50);
  --bg-secondary: var(--neutral-100);
  --bg-tertiary: var(--neutral-200);
  --bg-elevated: var(--neutral-50);
  --bg-hover: var(--neutral-100);
  --bg-overlay: rgba(255, 255, 255, 0.8);
  --bg-glass: rgba(0, 0, 0, 0.05);

  /* Text Colors */
  --text-primary: var(--neutral-900);
  --text-secondary: var(--neutral-600);
  --text-tertiary: var(--neutral-500);
  --text-muted: var(--neutral-400);
  --text-inverse: var(--neutral-50);

  /* Border Colors */
  --border-primary: var(--neutral-200);
  --border-secondary: var(--neutral-300);

  /* Shadows for Light Theme */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);

  /* Status Background Colors for Light Theme */
  --success-bg: var(--success-50);
  --warning-bg: var(--warning-50);
  --error-bg: var(--error-50);
  --info-bg: var(--info-50);

  /* Alert Colors for Light Theme */
  --widdx-alert-success-bg: var(--success-50);
  --widdx-alert-success-text: var(--success-700);
  --widdx-alert-success-border: var(--success-200);

  --widdx-alert-error-bg: var(--error-50);
  --widdx-alert-error-text: var(--error-700);
  --widdx-alert-error-border: var(--error-200);

  --widdx-alert-warning-bg: var(--warning-50);
  --widdx-alert-warning-text: var(--warning-700);
  --widdx-alert-warning-border: var(--warning-200);

  --widdx-alert-info-bg: var(--info-50);
  --widdx-alert-info-text: var(--info-700);
  --widdx-alert-info-border: var(--info-200);
}

/* ===== AUTO THEME (System Preference) ===== */
@media (prefers-color-scheme: light) {
  [data-theme="auto"] {
    /* Background Colors */
    --bg-primary: var(--neutral-50);
    --bg-secondary: var(--neutral-100);
    --bg-tertiary: var(--neutral-200);
    --bg-elevated: var(--neutral-50);
    --bg-hover: var(--neutral-100);
    --bg-overlay: rgba(255, 255, 255, 0.8);
    --bg-glass: rgba(0, 0, 0, 0.05);

    /* Text Colors */
    --text-primary: var(--neutral-900);
    --text-secondary: var(--neutral-600);
    --text-tertiary: var(--neutral-500);
    --text-muted: var(--neutral-400);
    --text-inverse: var(--neutral-50);

    /* Border Colors */
    --border-primary: var(--neutral-200);
    --border-secondary: var(--neutral-300);

    /* Light theme shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);
  }
}

/* ===== BASE STYLES ===== */
* {
  box-sizing: border-box;
}

html {
  font-family: var(--font-sans);
  font-size: 16px;
  line-height: var(--leading-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: var(--font-sans);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* ===== ENHANCED TYPOGRAPHY SYSTEM ===== */
/* Font Size Classes */
.text-xs { font-size: var(--text-xs); line-height: var(--leading-tight); }
.text-sm { font-size: var(--text-sm); line-height: var(--leading-snug); }
.text-base { font-size: var(--text-base); line-height: var(--leading-normal); }
.text-lg { font-size: var(--text-lg); line-height: var(--leading-relaxed); }
.text-xl { font-size: var(--text-xl); line-height: var(--leading-relaxed); }
.text-2xl { font-size: var(--text-2xl); line-height: var(--leading-tight); }
.text-3xl { font-size: var(--text-3xl); line-height: var(--leading-tight); }
.text-4xl { font-size: var(--text-4xl); line-height: var(--leading-none); }
.text-5xl { font-size: var(--text-5xl); line-height: var(--leading-none); }

/* Font Weight Classes */
.font-thin { font-weight: var(--font-thin); }
.font-light { font-weight: var(--font-light); }
.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }
.font-extrabold { font-weight: var(--font-extrabold); }
.font-black { font-weight: var(--font-black); }

/* Font Family Classes */
.font-sans { font-family: var(--font-sans); }
.font-mono { font-family: var(--font-mono); }

/* Line Height Classes */
.leading-none { line-height: var(--leading-none); }
.leading-tight { line-height: var(--leading-tight); }
.leading-snug { line-height: var(--leading-snug); }
.leading-normal { line-height: var(--leading-normal); }
.leading-relaxed { line-height: var(--leading-relaxed); }
.leading-loose { line-height: var(--leading-loose); }

/* Text Color Classes */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-muted { color: var(--text-muted); }
.text-inverse { color: var(--text-inverse); }

/* Legacy Typography Classes (Backward Compatibility) */
.widdx-text-xs { font-size: var(--text-xs); line-height: var(--leading-tight); }
.widdx-text-sm { font-size: var(--text-sm); line-height: var(--leading-snug); }
.widdx-text-base { font-size: var(--text-base); line-height: var(--leading-normal); }
.widdx-text-lg { font-size: var(--text-lg); line-height: var(--leading-relaxed); }
.widdx-text-xl { font-size: var(--text-xl); line-height: var(--leading-relaxed); }
.widdx-text-2xl { font-size: var(--text-2xl); line-height: var(--leading-tight); }
.widdx-text-3xl { font-size: var(--text-3xl); line-height: var(--leading-tight); }

.widdx-font-light { font-weight: var(--font-light); }
.widdx-font-normal { font-weight: var(--font-normal); }
.widdx-font-medium { font-weight: var(--font-medium); }
.widdx-font-semibold { font-weight: var(--font-semibold); }
.widdx-font-bold { font-weight: var(--font-bold); }

/* ===== ENHANCED CONTAINER SYSTEM ===== */
/* Base Container */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

/* Container Size Variants */
.container-xs { max-width: var(--container-xs); }
.container-sm { max-width: var(--container-sm); }
.container-md { max-width: var(--container-md); }
.container-lg { max-width: var(--container-lg); }
.container-xl { max-width: var(--container-xl); }
.container-2xl { max-width: var(--container-2xl); }
.container-3xl { max-width: var(--container-3xl); }
.container-4xl { max-width: var(--container-4xl); }
.container-5xl { max-width: var(--container-5xl); }
.container-6xl { max-width: var(--container-6xl); }
.container-7xl { max-width: var(--container-7xl); }

/* Responsive Container Breakpoints */
@media (min-width: 475px) {
  .container { max-width: var(--breakpoint-xs); }
}

@media (min-width: 640px) {
  .container { max-width: var(--breakpoint-sm); }
}

@media (min-width: 768px) {
  .container { max-width: var(--breakpoint-md); }
}

@media (min-width: 1024px) {
  .container { max-width: var(--breakpoint-lg); }
}

@media (min-width: 1280px) {
  .container { max-width: var(--breakpoint-xl); }
}

@media (min-width: 1536px) {
  .container { max-width: var(--breakpoint-2xl); }
}

/* Container Queries Support */
@container (min-width: 320px) {
  .container-query-xs { display: block; }
}

@container (min-width: 640px) {
  .container-query-sm { display: block; }
}

@container (min-width: 768px) {
  .container-query-md { display: block; }
}

@container (min-width: 1024px) {
  .container-query-lg { display: block; }
}

/* ===== ENHANCED LAYOUT COMPONENTS ===== */
/* Flexbox Utilities */
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

/* Flex Alignment */
.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }
.items-baseline { align-items: baseline; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

/* Gap Utilities */
.gap-0 { gap: var(--space-0); }
.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-5 { gap: var(--space-5); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }
.gap-10 { gap: var(--space-10); }
.gap-12 { gap: var(--space-12); }

/* Grid System */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

/* Legacy Layout Components (Backward Compatibility) */
.widdx-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.widdx-flex { display: flex; }
.widdx-flex-col { flex-direction: column; }
.widdx-items-center { align-items: center; }
.widdx-justify-between { justify-content: space-between; }
.widdx-justify-center { justify-content: center; }
.widdx-gap-sm { gap: var(--space-2); }
.widdx-gap-md { gap: var(--space-4); }
.widdx-gap-lg { gap: var(--space-6); }

/* ===== ENHANCED BUTTON SYSTEM ===== */
/* Base Button Styles */
.btn, .widdx-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border: none;
  border-radius: var(--radius-lg);
  font-family: var(--font-sans);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.btn:disabled, .widdx-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.btn:focus-visible, .widdx-btn:focus-visible {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* Primary Button with Gradient */
.btn-primary, .widdx-btn-primary {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled), .widdx-btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-primary:active:not(:disabled), .widdx-btn-primary:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Secondary Button */
.btn-secondary, .widdx-btn-secondary {
  background-color: var(--bg-elevated);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover:not(:disabled), .widdx-btn-secondary:hover:not(:disabled) {
  background-color: var(--bg-hover);
  border-color: var(--border-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Ghost Button */
.btn-ghost, .widdx-btn-ghost {
  background-color: transparent;
  color: var(--text-secondary);
}

.btn-ghost:hover:not(:disabled), .widdx-btn-ghost:hover:not(:disabled) {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

/* Gradient Button */
.btn-gradient {
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-600));
  color: white;
  box-shadow: var(--shadow-glow);
}

.btn-gradient:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-700));
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow-lg);
}

/* Success Button */
.btn-success {
  background: linear-gradient(135deg, var(--success-500), var(--success-600));
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-success:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--success-600), var(--success-700));
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Error Button */
.btn-error {
  background: linear-gradient(135deg, var(--error-500), var(--error-600));
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-error:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--error-600), var(--error-700));
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Button Sizes */
.btn-xs {
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-xs);
  gap: var(--space-1);
}

.btn-sm, .widdx-btn-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-sm);
  gap: var(--space-1-5);
}

.btn-md {
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-base);
  gap: var(--space-2);
}

.btn-lg, .widdx-btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-lg);
  gap: var(--space-2-5);
}

.btn-xl {
  padding: var(--space-5) var(--space-8);
  font-size: var(--text-xl);
  gap: var(--space-3);
}

/* Button Shapes */
.btn-rounded {
  border-radius: var(--radius-full);
}

.btn-square {
  border-radius: var(--radius-none);
}

/* Icon Buttons */
.btn-icon {
  padding: var(--space-3);
  aspect-ratio: 1;
}

.btn-icon.btn-sm {
  padding: var(--space-2);
}

.btn-icon.btn-lg {
  padding: var(--space-4);
}

/* Loading State */
.btn-loading {
  position: relative;
  color: transparent !important;
}

.btn-loading::after {
  content: '';
  position: absolute;
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  color: inherit;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* ===== INPUT COMPONENTS ===== */
.widdx-input {
  width: 100%;
  padding: var(--widdx-space-sm) var(--widdx-space-md);
  background-color: var(--widdx-bg-secondary);
  border: 1px solid var(--widdx-border-primary);
  border-radius: var(--widdx-radius-md);
  color: var(--widdx-text-primary);
  font-family: inherit;
  font-size: 1rem;
  transition: all var(--widdx-transition-fast);
}

.widdx-input:focus {
  outline: none;
  border-color: var(--widdx-border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.widdx-input::placeholder {
  color: var(--widdx-text-muted);
}

.widdx-textarea {
  resize: vertical;
  min-height: 2.5rem;
}

/* ===== ALERT COMPONENT ===== */
.widdx-alert {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 0.5rem;
    border: 1px solid;
    border-color: var(--widdx-border-primary);
    background-color: var(--widdx-bg-elevated);
    color: var(--widdx-text-primary);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    transition: all 200ms ease-in-out;
    opacity: 0.95;
}

.widdx-alert-error {
    background-color: var(--widdx-alert-error-bg);
    border-color: var(--widdx-alert-error-border);
    color: var(--widdx-alert-error-text);
}

.widdx-alert-success {
    background-color: var(--widdx-alert-success-bg);
    border-color: var(--widdx-alert-success-border);
    color: var(--widdx-alert-success-text);
}

.widdx-alert-warning {
    background-color: var(--widdx-alert-warning-bg);
    border-color: var(--widdx-alert-warning-border);
    color: var(--widdx-alert-warning-text);
}

.widdx-alert-info {
    background-color: var(--widdx-alert-info-bg);
    border-color: var(--widdx-alert-info-border);
    color: var(--widdx-alert-info-text);
}

.widdx-alert-close {
    margin-left: 1rem;
    color: currentColor;
    opacity: 0.5;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.25rem;
    line-height: 1;
    transition: opacity 200ms ease-in-out;
}

.widdx-alert-close:hover {
    opacity: 1;
}

/* Alert positions */
.widdx-alert-top-right {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 50;
    width: 100%;
    max-width: 20rem;
}

.widdx-alert-top-center {
    position: fixed;
    top: 1rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 50;
    width: 100%;
    max-width: 28rem;
}

.widdx-alert-bottom-right {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    z-index: 50;
    width: 100%;
    max-width: 20rem;
}

/* Animation classes */
.widdx-alert-enter {
    animation: slideInRight 0.3s ease-out forwards;
}

.widdx-alert-exit {
    animation: fadeIn 0.3s ease-out reverse forwards;
}

/* ===== CARD COMPONENTS ===== */
.widdx-card {
  background-color: var(--widdx-bg-secondary);
  border: 1px solid var(--widdx-border-primary);
  border-radius: var(--widdx-radius-lg);
  padding: var(--widdx-space-lg);
  box-shadow: var(--widdx-shadow-sm);
}

.widdx-card-elevated {
  background-color: var(--widdx-bg-elevated);
  box-shadow: var(--widdx-shadow-md);
}

/* ===== UTILITY CLASSES ===== */
.widdx-hidden { display: none; }
.widdx-visible { display: block; }
.widdx-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Spacing utilities */
.widdx-p-sm { padding: var(--widdx-space-sm); }
.widdx-p-md { padding: var(--widdx-space-md); }
.widdx-p-lg { padding: var(--widdx-space-lg); }

.widdx-m-sm { margin: var(--widdx-space-sm); }
.widdx-m-md { margin: var(--widdx-space-md); }
.widdx-m-lg { margin: var(--widdx-space-lg); }

/* ===== ANIMATIONS ===== */
@keyframes widdx-fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes widdx-slide-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes widdx-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.widdx-animate-fade-in {
  animation: widdx-fade-in var(--widdx-transition-normal);
}

.widdx-animate-slide-up {
  animation: widdx-slide-up var(--widdx-transition-normal);
}

.widdx-animate-pulse {
  animation: widdx-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* ===== ENHANCED RESPONSIVE UTILITIES ===== */

/* Mobile First Responsive Design */
/* Extra Small Devices (phones, 475px and down) */
@media (max-width: 474px) {
  .xs\:hidden { display: none; }
  .xs\:block { display: block; }
  .xs\:flex { display: flex; }
  .xs\:grid { display: grid; }

  .xs\:text-xs { font-size: var(--text-xs); }
  .xs\:text-sm { font-size: var(--text-sm); }
  .xs\:text-base { font-size: var(--text-base); }

  .xs\:p-2 { padding: var(--space-2); }
  .xs\:p-3 { padding: var(--space-3); }
  .xs\:p-4 { padding: var(--space-4); }

  .xs\:gap-2 { gap: var(--space-2); }
  .xs\:gap-3 { gap: var(--space-3); }
  .xs\:gap-4 { gap: var(--space-4); }

  .xs\:flex-col { flex-direction: column; }
  .xs\:items-center { align-items: center; }
  .xs\:justify-center { justify-content: center; }
}

/* Small Devices (landscape phones, 640px and down) */
@media (max-width: 639px) {
  .sm\:hidden { display: none; }
  .sm\:block { display: block; }
  .sm\:flex { display: flex; }
  .sm\:grid { display: grid; }

  .sm\:text-sm { font-size: var(--text-sm); }
  .sm\:text-base { font-size: var(--text-base); }
  .sm\:text-lg { font-size: var(--text-lg); }

  .sm\:p-2 { padding: var(--space-2); }
  .sm\:p-3 { padding: var(--space-3); }
  .sm\:p-4 { padding: var(--space-4); }

  .sm\:gap-2 { gap: var(--space-2); }
  .sm\:gap-3 { gap: var(--space-3); }
  .sm\:gap-4 { gap: var(--space-4); }

  .sm\:flex-col { flex-direction: column; }
  .sm\:items-center { align-items: center; }
  .sm\:justify-center { justify-content: center; }

  /* Mobile-specific button adjustments */
  .btn, .widdx-btn {
    min-height: 44px; /* Touch-friendly minimum */
    padding: var(--space-3) var(--space-4);
  }

  .btn-sm, .widdx-btn-sm {
    min-height: 36px;
    padding: var(--space-2) var(--space-3);
  }
}

/* Medium Devices (tablets, 768px and up) */
@media (min-width: 768px) {
  .md\:hidden { display: none; }
  .md\:block { display: block; }
  .md\:flex { display: flex; }
  .md\:grid { display: grid; }

  .md\:text-base { font-size: var(--text-base); }
  .md\:text-lg { font-size: var(--text-lg); }
  .md\:text-xl { font-size: var(--text-xl); }
  .md\:text-2xl { font-size: var(--text-2xl); }

  .md\:p-4 { padding: var(--space-4); }
  .md\:p-6 { padding: var(--space-6); }
  .md\:p-8 { padding: var(--space-8); }

  .md\:gap-4 { gap: var(--space-4); }
  .md\:gap-6 { gap: var(--space-6); }
  .md\:gap-8 { gap: var(--space-8); }

  .md\:flex-row { flex-direction: row; }
  .md\:items-start { align-items: flex-start; }
  .md\:justify-between { justify-content: space-between; }

  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

/* Large Devices (desktops, 1024px and up) */
@media (min-width: 1024px) {
  .lg\:hidden { display: none; }
  .lg\:block { display: block; }
  .lg\:flex { display: flex; }
  .lg\:grid { display: grid; }

  .lg\:text-lg { font-size: var(--text-lg); }
  .lg\:text-xl { font-size: var(--text-xl); }
  .lg\:text-2xl { font-size: var(--text-2xl); }
  .lg\:text-3xl { font-size: var(--text-3xl); }

  .lg\:p-6 { padding: var(--space-6); }
  .lg\:p-8 { padding: var(--space-8); }
  .lg\:p-12 { padding: var(--space-12); }

  .lg\:gap-6 { gap: var(--space-6); }
  .lg\:gap-8 { gap: var(--space-8); }
  .lg\:gap-12 { gap: var(--space-12); }

  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
}

/* Extra Large Devices (large desktops, 1280px and up) */
@media (min-width: 1280px) {
  .xl\:hidden { display: none; }
  .xl\:block { display: block; }
  .xl\:flex { display: flex; }
  .xl\:grid { display: grid; }

  .xl\:text-xl { font-size: var(--text-xl); }
  .xl\:text-2xl { font-size: var(--text-2xl); }
  .xl\:text-3xl { font-size: var(--text-3xl); }
  .xl\:text-4xl { font-size: var(--text-4xl); }

  .xl\:p-8 { padding: var(--space-8); }
  .xl\:p-12 { padding: var(--space-12); }
  .xl\:p-16 { padding: var(--space-16); }

  .xl\:gap-8 { gap: var(--space-8); }
  .xl\:gap-12 { gap: var(--space-12); }
  .xl\:gap-16 { gap: var(--space-16); }

  .xl\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .xl\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
  .xl\:grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }
}

/* 2XL Devices (larger desktops, 1536px and up) */
@media (min-width: 1536px) {
  .\32xl\:hidden { display: none; }
  .\32xl\:block { display: block; }
  .\32xl\:flex { display: flex; }
  .\32xl\:grid { display: grid; }

  .\32xl\:text-2xl { font-size: var(--text-2xl); }
  .\32xl\:text-3xl { font-size: var(--text-3xl); }
  .\32xl\:text-4xl { font-size: var(--text-4xl); }
  .\32xl\:text-5xl { font-size: var(--text-5xl); }

  .\32xl\:p-12 { padding: var(--space-12); }
  .\32xl\:p-16 { padding: var(--space-16); }
  .\32xl\:p-24 { padding: var(--space-24); }

  .\32xl\:gap-12 { gap: var(--space-12); }
  .\32xl\:gap-16 { gap: var(--space-16); }
  .\32xl\:gap-24 { gap: var(--space-24); }
}

/* Legacy Mobile Responsive (Backward Compatibility) */
@media (max-width: 768px) {
  .widdx-container {
    padding: 0 var(--space-2);
  }

  .widdx-btn {
    padding: var(--space-2) var(--space-3);
    min-height: 44px;
  }

  /* Mobile typography adjustments */
  .text-3xl, .widdx-text-3xl { font-size: var(--text-2xl); }
  .text-4xl { font-size: var(--text-3xl); }
  .text-5xl { font-size: var(--text-4xl); }
}

/* Print Styles */
@media print {
  .print\:hidden { display: none !important; }
  .print\:block { display: block !important; }

  /* Optimize for print */
  body {
    background: white !important;
    color: black !important;
  }

  .btn, .widdx-btn {
    border: 1px solid #000 !important;
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .btn, .widdx-btn {
    transition: none !important;
  }

  .btn:hover, .widdx-btn:hover {
    transform: none !important;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --border-primary: #000000;
    --border-secondary: #333333;
    --text-secondary: #000000;
    --text-tertiary: #333333;
  }

  [data-theme="light"] {
    --border-primary: #000000;
    --border-secondary: #333333;
    --text-secondary: #000000;
    --text-tertiary: #333333;
  }

  .btn, .widdx-btn {
    border: 2px solid currentColor !important;
  }
}
