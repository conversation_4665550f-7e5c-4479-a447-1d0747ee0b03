<?php

namespace App\Services;

use App\Models\ThinkingSession;
use Illuminate\Support\Facades\Log;

class DeepThinkingService
{
    private DeepSeekClient $deepSeekClient;
    private GeminiClient $geminiClient;
    private TranslationService $translation;
    private FeatureToggleService $featureToggle;

    // Thinking modes
    public const MODE_ANALYTICAL = 'analytical';
    public const MODE_CREATIVE = 'creative';
    public const MODE_LOGICAL = 'logical';
    public const MODE_COMPREHENSIVE = 'comprehensive';

    public function __construct(
        DeepSeekClient $deepSeekClient,
        GeminiClient $geminiClient,
        TranslationService $translation,
        FeatureToggleService $featureToggle
    ) {
        $this->deepSeekClient = $deepSeekClient;
        $this->geminiClient = $geminiClient;
        $this->translation = $translation;
        $this->featureToggle = $featureToggle;
    }

    /**
     * Process message with deep thinking
     */
    public function processWithDeepThinking(string $message, array $conversationHistory = [], array $options = []): array
    {
        $startTime = microtime(true);
        $sessionId = $options['session_id'] ?? 'default';
        $userIdentifier = $options['user_identifier'] ?? $sessionId;

        try {
            // Check if deep thinking is enabled for user
            if (!$this->featureToggle->isFeatureEnabled($userIdentifier, FeatureToggleService::FEATURE_DEEP_THINKING)) {
                return [
                    'success' => false,
                    'content' => 'Deep thinking mode is not enabled for your account.',
                    'error' => 'Feature not enabled'
                ];
            }

            // Get user settings for deep thinking
            $settings = $this->featureToggle->getFeatureSettings($userIdentifier, FeatureToggleService::FEATURE_DEEP_THINKING);

            // Determine thinking mode
            $thinkingMode = $this->determineThinkingMode($message, $options);

            // Get thinking depth level
            $depthLevel = min($settings['max_thinking_depth'] ?? 3, 5);

            Log::info('Starting deep thinking process', [
                'session_id' => $sessionId,
                'thinking_mode' => $thinkingMode,
                'depth_level' => $depthLevel,
                'message_length' => strlen($message)
            ]);

            // Perform multi-stage thinking
            $thinkingResult = $this->performDeepThinking($message, $thinkingMode, $depthLevel, $conversationHistory, $options);

            // Calculate processing time
            $processingTime = (microtime(true) - $startTime) * 1000;

            // Record thinking session
            $this->recordThinkingSession($sessionId, $thinkingMode, $message, $thinkingResult, $processingTime);

            // Format response based on user preferences
            $response = $this->formatThinkingResponse($thinkingResult, $settings, $options);

            return [
                'success' => true,
                'content' => $response,
                'thinking_mode' => $thinkingMode,
                'depth_level' => $depthLevel,
                'processing_time' => $processingTime,
                'metadata' => [
                    'thinking_steps' => count($thinkingResult['steps']),
                    'confidence_score' => $thinkingResult['confidence_score'],
                    'sources_consulted' => $thinkingResult['sources_consulted'] ?? []
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Deep thinking process failed', [
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'content' => 'Sorry, I encountered an error during deep thinking. Please try again.',
                'error' => $e->getMessage(),
                'processing_time' => (microtime(true) - $startTime) * 1000
            ];
        }
    }

    /**
     * Perform multi-stage deep thinking
     */
    private function performDeepThinking(string $message, string $mode, int $depthLevel, array $conversationHistory, array $options): array
    {
        // Check if we should use quick thinking mode (for testing or when performance is critical)
        if (isset($options['quick_thinking']) && $options['quick_thinking']) {
            return $this->performQuickThinking($message, $mode, $conversationHistory, $options);
        }

        $thinkingSteps = [];
        $currentThought = $message;
        $confidenceScore = 0.0;
        $sourcesConsulted = [];

        // Stage 1: Initial Analysis
        $analysisStep = $this->performThinkingStep(
            'initial_analysis',
            "Analyze this question/request deeply: {$currentThought}",
            $mode,
            $conversationHistory,
            $options
        );
        $thinkingSteps[] = $analysisStep;
        $currentThought = $analysisStep['output'];

        // Stage 2: Multi-perspective thinking (based on depth level)
        for ($i = 1; $i < $depthLevel; $i++) {
            $perspectiveStep = $this->performThinkingStep(
                "perspective_analysis_{$i}",
                "Consider this from a different angle and add deeper insights: {$currentThought}",
                $mode,
                $conversationHistory,
                $options
            );
            $thinkingSteps[] = $perspectiveStep;
            $currentThought = $perspectiveStep['output'];
        }

        // Stage 3: Synthesis and conclusion
        $synthesisStep = $this->performThinkingStep(
            'synthesis',
            "Synthesize all previous thoughts and provide a comprehensive conclusion: {$currentThought}",
            $mode,
            $conversationHistory,
            $options
        );
        $thinkingSteps[] = $synthesisStep;

        // Calculate overall confidence score
        $confidenceScore = $this->calculateConfidenceScore($thinkingSteps);

        return [
            'steps' => $thinkingSteps,
            'final_conclusion' => $synthesisStep['output'],
            'confidence_score' => $confidenceScore,
            'thinking_mode' => $mode,
            'depth_level' => $depthLevel,
            'sources_consulted' => $sourcesConsulted
        ];
    }

    /**
     * Perform quick thinking (single-step) for testing or when performance is critical
     */
    private function performQuickThinking(string $message, string $mode, array $conversationHistory, array $options): array
    {
        // Create a comprehensive prompt that simulates multi-stage thinking
        $prompt = "You are WIDDX AI in deep thinking mode. Please analyze the following question/request thoroughly:

Question: {$message}

Please think through this in the following steps:
1. Initial analysis - Break down the core components of the question
2. Multiple perspectives - Consider different angles and viewpoints
3. Synthesis - Provide a comprehensive conclusion

Your response should be structured, thoughtful, and demonstrate deep analytical thinking.";

        // Get a single comprehensive response
        try {
            $messages = [
                ['role' => 'system', 'content' => $this->buildThinkingSystemPrompt($mode, 'comprehensive')],
                ['role' => 'user', 'content' => $prompt]
            ];

            $response = $this->geminiClient->chat($messages, [
                'temperature' => 0.7,
                'max_tokens' => 2000,
                'timeout' => 20
            ]);

            if (!$response['success']) {
                throw new \Exception('Failed to get thinking response');
            }

            $content = $response['content'];

            // Create simulated thinking steps
            $thinkingSteps = [
                [
                    'step_type' => 'quick_thinking',
                    'input' => $message,
                    'output' => $content,
                    'processing_time_ms' => 1000,
                    'model_used' => 'gemini',
                    'success' => true
                ]
            ];

            return [
                'steps' => $thinkingSteps,
                'final_conclusion' => $content,
                'confidence_score' => 0.8,
                'thinking_mode' => $mode,
                'depth_level' => 1,
                'sources_consulted' => []
            ];
        } catch (\Exception $e) {
            Log::error('Quick thinking failed', [
                'error' => $e->getMessage()
            ]);

            // Return a basic response
            return [
                'steps' => [
                    [
                        'step_type' => 'quick_thinking',
                        'input' => $message,
                        'output' => 'I apologize, but I encountered an issue while processing your request in deep thinking mode. Let me provide a simpler response instead.',
                        'processing_time_ms' => 100,
                        'model_used' => 'fallback',
                        'success' => false
                    ]
                ],
                'final_conclusion' => 'I apologize, but I encountered an issue while processing your request in deep thinking mode. Let me provide a simpler response instead.',
                'confidence_score' => 0.3,
                'thinking_mode' => $mode,
                'depth_level' => 1,
                'sources_consulted' => []
            ];
        }
    }

    /**
     * Perform individual thinking step
     */
    private function performThinkingStep(string $stepType, string $prompt, string $mode, array $conversationHistory, array $options): array
    {
        $stepStartTime = microtime(true);

        try {
            // Create thinking-specific system prompt
            $systemPrompt = $this->buildThinkingSystemPrompt($mode, $stepType);

            // Get response from AI model
            $response = $this->getThinkingResponse($systemPrompt, $prompt, $conversationHistory, $options);

            $processingTime = (microtime(true) - $stepStartTime) * 1000;

            return [
                'step_type' => $stepType,
                'input' => $prompt,
                'output' => $response['content'] ?? '',
                'processing_time_ms' => $processingTime,
                'model_used' => $response['model'] ?? 'unknown',
                'success' => true
            ];

        } catch (\Exception $e) {
            Log::warning('Thinking step failed', [
                'step_type' => $stepType,
                'error' => $e->getMessage()
            ]);

            return [
                'step_type' => $stepType,
                'input' => $prompt,
                'output' => 'Unable to complete this thinking step.',
                'processing_time_ms' => (microtime(true) - $stepStartTime) * 1000,
                'model_used' => 'error',
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Build thinking-specific system prompt
     */
    private function buildThinkingSystemPrompt(string $mode, string $stepType): string
    {
        $basePrompt = "You are WIDDX, an advanced AI with deep thinking capabilities. ";

        $modePrompts = [
            self::MODE_ANALYTICAL => "Focus on logical analysis, breaking down complex problems systematically.",
            self::MODE_CREATIVE => "Think creatively and explore innovative solutions and perspectives.",
            self::MODE_LOGICAL => "Apply strict logical reasoning and evidence-based thinking.",
            self::MODE_COMPREHENSIVE => "Consider all aspects comprehensively, including implications and consequences."
        ];

        $stepPrompts = [
            'initial_analysis' => "Start with a thorough initial analysis of the question or problem.",
            'synthesis' => "Synthesize all previous thoughts into a coherent and comprehensive conclusion."
        ];

        $modeInstruction = $modePrompts[$mode] ?? $modePrompts[self::MODE_ANALYTICAL];
        $stepInstruction = $stepPrompts[$stepType] ?? "Continue the deep thinking process with additional insights.";

        return $basePrompt . $modeInstruction . " " . $stepInstruction . "

Think step by step and show your reasoning process. Be thorough but concise.";
    }

    /**
     * Get thinking response from AI model
     */
    private function getThinkingResponse(string $systemPrompt, string $userPrompt, array $conversationHistory, array $options): array
    {
        try {
            // Try DeepSeek first (better for analytical thinking)
            $messages = [
                ['role' => 'system', 'content' => $systemPrompt],
                ['role' => 'user', 'content' => $userPrompt]
            ];

            $response = $this->deepSeekClient->chat($messages, [
                'temperature' => 0.7,
                'max_tokens' => 1500,
                'timeout' => 15 // Shorter timeout for thinking steps
            ]);

            if ($response['success']) {
                return [
                    'content' => $response['content'],
                    'model' => 'deepseek'
                ];
            }
        } catch (\Exception $e) {
            Log::warning('DeepSeek thinking failed, trying Gemini', [
                'error' => $e->getMessage()
            ]);
        }

        // Fallback to Gemini
        try {
            $messages = [
                ['role' => 'system', 'content' => $systemPrompt],
                ['role' => 'user', 'content' => $userPrompt]
            ];

            $response = $this->geminiClient->chat($messages, [
                'temperature' => 0.7,
                'max_tokens' => 1500,
                'timeout' => 10 // Even shorter timeout for fallback
            ]);

            if ($response['success']) {
                return [
                    'content' => $response['content'],
                    'model' => 'gemini'
                ];
            }
        } catch (\Exception $e) {
            Log::error('Both AI models failed for thinking', [
                'error' => $e->getMessage()
            ]);
        }

        throw new \Exception('All AI models failed to provide thinking response');
    }

    /**
     * Determine appropriate thinking mode based on message
     */
    private function determineThinkingMode(string $message, array $options): string
    {
        // Check if mode is explicitly specified
        if (isset($options['thinking_mode']) && in_array($options['thinking_mode'], [
            self::MODE_ANALYTICAL, self::MODE_CREATIVE, self::MODE_LOGICAL, self::MODE_COMPREHENSIVE
        ])) {
            return $options['thinking_mode'];
        }

        // Auto-detect based on message content
        $message = strtolower($message);

        if (preg_match('/\b(create|design|imagine|innovate|brainstorm)\b/', $message)) {
            return self::MODE_CREATIVE;
        }

        if (preg_match('/\b(analyze|examine|study|investigate|research)\b/', $message)) {
            return self::MODE_ANALYTICAL;
        }

        if (preg_match('/\b(prove|logic|reason|deduce|conclude)\b/', $message)) {
            return self::MODE_LOGICAL;
        }

        // Default to comprehensive for complex questions
        return self::MODE_COMPREHENSIVE;
    }

    /**
     * Calculate confidence score based on thinking steps
     */
    private function calculateConfidenceScore(array $thinkingSteps): float
    {
        $successfulSteps = array_filter($thinkingSteps, fn($step) => $step['success']);
        $successRate = count($successfulSteps) / max(count($thinkingSteps), 1);

        // Base confidence on success rate and depth
        $baseConfidence = $successRate * 0.8;
        $depthBonus = min(count($thinkingSteps) * 0.05, 0.2);

        return min($baseConfidence + $depthBonus, 1.0);
    }

    /**
     * Format thinking response for user
     */
    private function formatThinkingResponse(array $thinkingResult, array $settings, array $options): string
    {
        $showProcess = $settings['show_thinking_process'] ?? true;
        $isArabic = ($options['target_language_code'] ?? 'en') === 'ar';

        if ($isArabic) {
            $response = "🧠 **التفكير العميق** (الوضع: {$thinkingResult['thinking_mode']})\n\n";
        } else {
            $response = "🧠 **Deep Thinking** (Mode: {$thinkingResult['thinking_mode']})\n\n";
        }

        if ($showProcess && !empty($thinkingResult['steps'])) {
            if ($isArabic) {
                $response .= "**🔍 عملية التفكير:**\n\n";
            } else {
                $response .= "**🔍 Thinking Process:**\n\n";
            }

            foreach ($thinkingResult['steps'] as $index => $step) {
                $stepNumber = $index + 1;
                $response .= "**{$stepNumber}.** {$step['output']}\n\n";
            }

            if ($isArabic) {
                $response .= "---\n\n**🎯 الخلاصة النهائية:**\n\n";
            } else {
                $response .= "---\n\n**🎯 Final Conclusion:**\n\n";
            }
        }

        $response .= $thinkingResult['final_conclusion'];

        // Add confidence indicator
        $confidencePercent = round($thinkingResult['confidence_score'] * 100);
        if ($isArabic) {
            $response .= "\n\n📊 **مستوى الثقة:** {$confidencePercent}%";
        } else {
            $response .= "\n\n📊 **Confidence Level:** {$confidencePercent}%";
        }

        return $response;
    }

    /**
     * Record thinking session to database
     */
    private function recordThinkingSession(string $sessionId, string $thinkingMode, string $originalQuestion, array $thinkingResult, float $processingTime): void
    {
        try {
            ThinkingSession::recordSession([
                'session_id' => $sessionId,
                'thinking_mode' => $thinkingMode,
                'original_question' => $originalQuestion,
                'thinking_steps' => $thinkingResult['steps'],
                'thought_process' => json_encode($thinkingResult['steps']),
                'final_conclusion' => $thinkingResult['final_conclusion'],
                'thinking_depth_level' => $thinkingResult['depth_level'],
                'processing_time_ms' => $processingTime,
                'steps_count' => count($thinkingResult['steps']),
                'confidence_score' => $thinkingResult['confidence_score'],
                'sources_consulted' => $thinkingResult['sources_consulted'] ?? [],
                'was_successful' => true
            ]);
        } catch (\Exception $e) {
            Log::warning('Failed to record thinking session', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get thinking history for session
     */
    public function getThinkingHistory(string $sessionId, int $limit = 10): array
    {
        return ThinkingSession::getRecentSessions($sessionId, $limit)
            ->map(function ($session) {
                return [
                    'id' => $session->id,
                    'thinking_mode' => $session->thinking_mode,
                    'original_question' => $session->original_question,
                    'final_conclusion' => $session->final_conclusion,
                    'confidence_score' => $session->confidence_score,
                    'steps_count' => $session->steps_count,
                    'processing_time_ms' => $session->processing_time_ms,
                    'created_at' => $session->created_at->toISOString()
                ];
            })
            ->toArray();
    }

    /**
     * Get thinking statistics for user
     */
    public function getThinkingStats(string $sessionId): array
    {
        $sessions = ThinkingSession::where('session_id', $sessionId)
            ->where('was_successful', true)
            ->get();

        if ($sessions->isEmpty()) {
            return [
                'total_sessions' => 0,
                'average_confidence' => 0.0,
                'average_processing_time' => 0.0,
                'most_used_mode' => null,
                'total_thinking_time' => 0.0
            ];
        }

        $modeUsage = $sessions->groupBy('thinking_mode')
            ->map(fn($group) => $group->count())
            ->sortDesc();

        return [
            'total_sessions' => $sessions->count(),
            'average_confidence' => round($sessions->avg('confidence_score'), 2),
            'average_processing_time' => round($sessions->avg('processing_time_ms'), 2),
            'most_used_mode' => $modeUsage->keys()->first(),
            'total_thinking_time' => round($sessions->sum('processing_time_ms') / 1000, 2), // in seconds
            'mode_usage' => $modeUsage->toArray()
        ];
    }

    /**
     * Check if message requires deep thinking
     */
    public function shouldUseDeepThinking(string $message): bool
    {
        // Keywords that suggest complex thinking is needed
        $complexityIndicators = [
            'analyze', 'explain', 'compare', 'evaluate', 'discuss', 'examine',
            'investigate', 'explore', 'consider', 'assess', 'critique', 'review',
            'why', 'how', 'what if', 'pros and cons', 'advantages', 'disadvantages',
            'implications', 'consequences', 'relationship', 'connection',
            'تحليل', 'شرح', 'مقارنة', 'تقييم', 'مناقشة', 'فحص', 'استكشاف',
            'لماذا', 'كيف', 'ماذا لو', 'مزايا', 'عيوب', 'نتائج', 'علاقة'
        ];

        $message = strtolower($message);

        foreach ($complexityIndicators as $indicator) {
            if (strpos($message, strtolower($indicator)) !== false) {
                return true;
            }
        }

        // Check message length (longer messages often need deeper thinking)
        if (strlen($message) > 100) {
            return true;
        }

        // Check for question marks (questions often benefit from deep thinking)
        if (substr_count($message, '?') > 0 || substr_count($message, '؟') > 0) {
            return true;
        }

        return false;
    }

    /**
     * Get available thinking modes
     */
    public function getAvailableThinkingModes(): array
    {
        return [
            self::MODE_ANALYTICAL => [
                'name' => 'Analytical',
                'description' => 'Systematic logical analysis and problem breakdown',
                'best_for' => 'Complex problems, data analysis, research questions'
            ],
            self::MODE_CREATIVE => [
                'name' => 'Creative',
                'description' => 'Innovative and imaginative thinking approaches',
                'best_for' => 'Brainstorming, design challenges, artistic projects'
            ],
            self::MODE_LOGICAL => [
                'name' => 'Logical',
                'description' => 'Strict logical reasoning and evidence-based conclusions',
                'best_for' => 'Mathematical problems, logical puzzles, formal reasoning'
            ],
            self::MODE_COMPREHENSIVE => [
                'name' => 'Comprehensive',
                'description' => 'Holistic analysis considering all aspects and implications',
                'best_for' => 'Strategic decisions, policy analysis, complex scenarios'
            ]
        ];
    }
}
