/* WIDDX AI - Advanced Interactive Interface */

/* ===== CSS VARIABLES ===== */
:root {
  /* Colors - WIDDX Brand */
  --bg-primary: #000000;
  --bg-secondary: #0a0a0a;
  --bg-tertiary: #111111;
  --bg-elevated: #1a1a1a;
  --bg-hover: #222222;
  --bg-active: #2a2a2a;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-tertiary: #808080;
  --text-muted: #666666;
  --text-accent: #ff6b35;

  /* Accent Colors */
  --accent-primary: #ff6b35;
  --accent-secondary: #ff8c42;
  --accent-hover: #ff5722;
  --accent-light: rgba(255, 107, 53, 0.1);

  /* Border Colors */
  --border-primary: #333333;
  --border-secondary: #444444;
  --border-accent: #ff6b35;
  --border-focus: #ff8c42;

  /* Status Colors */
  --success: #4caf50;
  --warning: #ff9800;
  --error: #f44336;
  --info: #2196f3;

  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.6);

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.25s ease;
  --transition-slow: 0.35s ease;

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 9999px;

  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
}

/* ===== RESET & BASE ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== LAYOUT ===== */
.widdx-app {
  display: flex;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: relative;
}

.widdx-sidebar {
  width: 280px;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  transition: all var(--transition-normal);
  position: relative;
  z-index: var(--z-fixed);
}

.widdx-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

.widdx-header {
  height: 70px;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--space-lg);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: var(--z-sticky);
}

.widdx-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

.widdx-chat-area {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-lg);
  scroll-behavior: smooth;
}

.widdx-input-area {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
  padding: var(--space-lg);
  backdrop-filter: blur(10px);
}

/* ===== SIDEBAR ===== */
.grok-logo {
  padding: var(--space-lg);
  border-bottom: 1px solid var(--border-primary);
}

.grok-logo-content {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.grok-logo-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  font-size: 18px;
}

.grok-logo-text {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
}

.grok-nav {
  flex: 1;
  padding: var(--space-lg);
}

.grok-nav-section {
  margin-bottom: var(--space-xl);
}

.grok-nav-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--space-md);
}

.grok-nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-md);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  text-decoration: none;
  transition: all var(--transition-fast);
  margin-bottom: var(--space-xs);
  cursor: pointer;
}

.grok-nav-item:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.grok-nav-item.active {
  background: var(--accent-light);
  color: var(--accent-primary);
}

.grok-nav-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ===== HEADER ===== */
.grok-header-left {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.grok-header-right {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.grok-breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  color: var(--text-secondary);
  font-size: 14px;
}

.grok-breadcrumb-separator {
  color: var(--text-tertiary);
}

/* ===== BUTTONS ===== */
.grok-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--bg-elevated);
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.grok-btn:hover {
  background: var(--bg-hover);
  border-color: var(--border-secondary);
}

.grok-btn:active {
  transform: translateY(1px);
}

.grok-btn.primary {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
  color: white;
}

.grok-btn.primary:hover {
  background: var(--accent-hover);
  border-color: var(--accent-hover);
}

.grok-btn.secondary {
  background: transparent;
  border-color: var(--border-secondary);
}

.grok-btn.ghost {
  background: transparent;
  border: none;
  padding: var(--space-sm);
}

.grok-btn.icon-only {
  width: 36px;
  height: 36px;
  padding: 0;
}

/* ===== FEATURE TOGGLES ===== */
.grok-feature-toggles {
  display: flex;
  gap: var(--space-sm);
  flex-wrap: wrap;
}

.grok-toggle {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-full);
  background: var(--bg-elevated);
  color: var(--text-secondary);
  font-size: 13px;
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
}

.grok-toggle:hover {
  background: var(--bg-hover);
  border-color: var(--border-secondary);
}

.grok-toggle.active {
  background: var(--accent-light);
  border-color: var(--accent-primary);
  color: var(--accent-primary);
}

.grok-toggle-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .grok-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: var(--z-fixed);
    transform: translateX(-100%);
  }

  .grok-sidebar.open {
    transform: translateX(0);
  }

  .grok-main {
    width: 100%;
  }

  .grok-header {
    padding: 0 var(--space-md);
  }

  .grok-chat-area {
    padding: var(--space-md);
  }

  .grok-input-area {
    padding: var(--space-md);
  }

  .grok-feature-toggles {
    flex-direction: column;
    gap: var(--space-xs);
  }
}

/* ===== UTILITIES ===== */
.hidden { display: none !important; }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-sm { gap: var(--space-sm); }
.gap-md { gap: var(--space-md); }
.gap-lg { gap: var(--space-lg); }
.w-full { width: 100%; }
.h-full { height: 100%; }
.text-center { text-align: center; }
.font-bold { font-weight: 700; }
.font-medium { font-weight: 500; }
.text-sm { font-size: 14px; }
.text-xs { font-size: 12px; }
.opacity-50 { opacity: 0.5; }
.cursor-pointer { cursor: pointer; }
