/**
 * WIDDX AI - Enhanced Interactive Interface
 * Advanced features and animations
 */

class WiddxEnhancedInterface {
    constructor() {
        this.features = {
            search: false,
            deepSearch: false,
            thinkMode: false,
            imageGeneration: false,
            vision: false
        };
        
        this.isTyping = false;
        this.currentSession = null;
        this.messageHistory = [];
        this.notifications = [];
        
        this.init();
    }
    
    init() {
        this.setupEnhancedAnimations();
        this.setupFeatureToggles();
        this.setupNotificationSystem();
        this.setupAdvancedInteractions();
        this.setupKeyboardShortcuts();
        this.setupGestureSupport();
        
        console.log('✨ WIDDX Enhanced Interface initialized');
    }
    
    setupEnhancedAnimations() {
        // Intersection Observer for scroll animations
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animationPlayState = 'running';
                }
            });
        }, { threshold: 0.1 });
        
        // Observe all animatable elements
        document.querySelectorAll('.widdx-message, .widdx-feature-card').forEach(el => {
            observer.observe(el);
        });
    }
    
    setupFeatureToggles() {
        document.querySelectorAll('.widdx-feature-toggle').forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                const feature = e.currentTarget.dataset.feature;
                this.toggleFeature(feature, e.currentTarget);
            });
            
            // Add ripple effect
            toggle.addEventListener('click', this.createRippleEffect);
        });
    }
    
    toggleFeature(feature, element) {
        // Prevent auto-activation - only manual toggle
        const wasActive = this.features[feature];
        this.features[feature] = !wasActive;
        
        if (this.features[feature]) {
            element.classList.add('active');
            this.showNotification(`${this.getFeatureName(feature)} activated`, 'success');
            this.animateFeatureActivation(element);
        } else {
            element.classList.remove('active');
            this.showNotification(`${this.getFeatureName(feature)} deactivated`, 'info');
        }
        
        // Update all toggles for this feature
        document.querySelectorAll(`[data-feature="${feature}"]`).forEach(el => {
            if (this.features[feature]) {
                el.classList.add('active');
            } else {
                el.classList.remove('active');
            }
        });
        
        console.log(`Feature ${feature}: ${this.features[feature] ? 'ON' : 'OFF'}`);
    }
    
    getFeatureName(feature) {
        const names = {
            search: 'Web Search',
            deepSearch: 'Deep Search',
            thinkMode: 'Think Mode',
            imageGeneration: 'Image Generation',
            vision: 'Vision Analysis'
        };
        return names[feature] || feature;
    }
    
    animateFeatureActivation(element) {
        element.style.animation = 'widdx-bounce-in 0.6s ease-out';
        setTimeout(() => {
            element.style.animation = '';
        }, 600);
    }
    
    createRippleEffect(e) {
        const button = e.currentTarget;
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        const ripple = document.createElement('div');
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
        `;
        
        button.style.position = 'relative';
        button.style.overflow = 'hidden';
        button.appendChild(ripple);
        
        setTimeout(() => ripple.remove(), 600);
    }
    
    setupNotificationSystem() {
        // Create notification container
        if (!document.getElementById('widdx-notifications')) {
            const container = document.createElement('div');
            container.id = 'widdx-notifications';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                display: flex;
                flex-direction: column;
                gap: 10px;
                pointer-events: none;
            `;
            document.body.appendChild(container);
        }
    }
    
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `widdx-notification ${type}`;
        notification.style.pointerEvents = 'auto';
        
        const icon = this.getNotificationIcon(type);
        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <i class="${icon}" style="color: var(--accent-primary);"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" 
                        style="background: none; border: none; color: var(--text-secondary); cursor: pointer; margin-left: 10px;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        document.getElementById('widdx-notifications').appendChild(notification);
        
        // Auto remove
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.animation = 'widdx-slide-out 0.3s ease-out forwards';
                setTimeout(() => notification.remove(), 300);
            }
        }, duration);
        
        this.notifications.push({ message, type, timestamp: Date.now() });
    }
    
    getNotificationIcon(type) {
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        return icons[type] || icons.info;
    }
    
    setupAdvancedInteractions() {
        // Enhanced message interactions
        document.addEventListener('click', (e) => {
            if (e.target.closest('.widdx-message-bubble')) {
                this.handleMessageInteraction(e);
            }
        });
        
        // Feature card interactions
        document.querySelectorAll('.widdx-feature-card').forEach(card => {
            card.addEventListener('click', (e) => {
                const feature = e.currentTarget.dataset.feature;
                if (feature) {
                    this.toggleFeature(feature, e.currentTarget);
                    e.currentTarget.classList.toggle('active');
                }
            });
        });
        
        // Enhanced input interactions
        const textarea = document.getElementById('message-input');
        if (textarea) {
            textarea.addEventListener('focus', () => {
                textarea.parentElement.style.transform = 'translateY(-2px)';
            });
            
            textarea.addEventListener('blur', () => {
                textarea.parentElement.style.transform = 'translateY(0)';
            });
        }
    }
    
    handleMessageInteraction(e) {
        const bubble = e.target.closest('.widdx-message-bubble');
        
        // Add selection highlight
        bubble.style.animation = 'widdx-pulse 0.3s ease-out';
        setTimeout(() => {
            bubble.style.animation = '';
        }, 300);
    }
    
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Feature toggles with Alt + number
            if (e.altKey) {
                const shortcuts = {
                    '1': 'search',
                    '2': 'deepSearch', 
                    '3': 'thinkMode',
                    '4': 'imageGeneration',
                    '5': 'vision'
                };
                
                if (shortcuts[e.key]) {
                    e.preventDefault();
                    const toggle = document.querySelector(`[data-feature="${shortcuts[e.key]}"]`);
                    if (toggle) {
                        this.toggleFeature(shortcuts[e.key], toggle);
                    }
                }
            }
            
            // Enhanced shortcuts
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'n':
                        e.preventDefault();
                        this.newChat();
                        break;
                    case 'l':
                        e.preventDefault();
                        this.clearChat();
                        break;
                    case 'f':
                        e.preventDefault();
                        this.focusSearch();
                        break;
                }
            }
        });
    }
    
    setupGestureSupport() {
        let touchStartX = 0;
        let touchStartY = 0;
        
        document.addEventListener('touchstart', (e) => {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
        });
        
        document.addEventListener('touchend', (e) => {
            const touchEndX = e.changedTouches[0].clientX;
            const touchEndY = e.changedTouches[0].clientY;
            
            const deltaX = touchEndX - touchStartX;
            const deltaY = touchEndY - touchStartY;
            
            // Swipe gestures
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                if (deltaX > 0) {
                    // Swipe right - open sidebar
                    this.openSidebar();
                } else {
                    // Swipe left - close sidebar
                    this.closeSidebar();
                }
            }
        });
    }
    
    openSidebar() {
        const sidebar = document.querySelector('.widdx-sidebar');
        if (sidebar) {
            sidebar.classList.add('open');
        }
    }
    
    closeSidebar() {
        const sidebar = document.querySelector('.widdx-sidebar');
        if (sidebar) {
            sidebar.classList.remove('open');
        }
    }
    
    newChat() {
        // Reset features to manual state
        Object.keys(this.features).forEach(feature => {
            this.features[feature] = false;
        });
        
        // Update UI
        document.querySelectorAll('.widdx-feature-toggle').forEach(toggle => {
            toggle.classList.remove('active');
        });
        
        this.showNotification('New chat started', 'success');
        console.log('New chat - all features reset to manual');
    }
    
    clearChat() {
        const messagesContainer = document.getElementById('messages-container');
        if (messagesContainer) {
            messagesContainer.innerHTML = '';
            messagesContainer.style.display = 'none';
        }
        
        const welcomeScreen = document.getElementById('welcome-screen');
        if (welcomeScreen) {
            welcomeScreen.style.display = 'flex';
        }
        
        this.showNotification('Chat cleared', 'info');
    }
    
    focusSearch() {
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            messageInput.focus();
        }
    }
    
    // Enhanced message sending with feature awareness
    async sendMessage(message) {
        const activeFeatures = Object.keys(this.features).filter(f => this.features[f]);
        
        if (activeFeatures.length > 0) {
            this.showNotification(`Sending with: ${activeFeatures.map(f => this.getFeatureName(f)).join(', ')}`, 'info');
        }
        
        // Show enhanced thinking indicator if think mode is active
        if (this.features.thinkMode) {
            this.showEnhancedThinking();
        }
        
        // Simulate API call
        console.log('Sending message with features:', activeFeatures);
    }
    
    showEnhancedThinking() {
        const thinkingSteps = [
            'Analyzing your question...',
            'Searching for relevant information...',
            'Processing with deep thinking...',
            'Formulating comprehensive response...'
        ];
        
        // Implementation for enhanced thinking display
        console.log('Enhanced thinking mode activated');
    }
    
    // Feature state management
    getActiveFeatures() {
        return Object.keys(this.features).filter(f => this.features[f]);
    }
    
    hasActiveFeatures() {
        return this.getActiveFeatures().length > 0;
    }
    
    resetFeatures() {
        Object.keys(this.features).forEach(feature => {
            this.features[feature] = false;
        });
        
        document.querySelectorAll('.widdx-feature-toggle').forEach(toggle => {
            toggle.classList.remove('active');
        });
    }
}

// Add CSS for ripple animation
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(2);
            opacity: 0;
        }
    }
    
    @keyframes widdx-slide-out {
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Initialize enhanced interface
window.widdxEnhanced = new WiddxEnhancedInterface();

// Export for global access
window.WiddxEnhancedInterface = WiddxEnhancedInterface;
