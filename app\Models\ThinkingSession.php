<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ThinkingSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'session_id',
        'thinking_mode',
        'original_question',
        'thinking_steps',
        'thought_process',
        'final_conclusion',
        'thinking_depth_level',
        'processing_time_ms',
        'steps_count',
        'confidence_score',
        'sources_consulted',
        'was_successful',
        'error_message'
    ];

    protected $casts = [
        'thinking_steps' => 'array',
        'sources_consulted' => 'array',
        'was_successful' => 'boolean',
        'confidence_score' => 'float'
    ];

    /**
     * Get recent thinking sessions for session
     */
    public static function getRecentSessions(string $sessionId, int $limit = 10)
    {
        return self::where('session_id', $sessionId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get successful sessions by thinking mode
     */
    public static function getSuccessfulSessionsByMode(string $thinkingMode, int $limit = 50)
    {
        return self::where('thinking_mode', $thinkingMode)
            ->where('was_successful', true)
            ->orderBy('confidence_score', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Record new thinking session
     */
    public static function recordSession(array $sessionData)
    {
        return self::create($sessionData);
    }

    /**
     * Get average confidence score for mode
     */
    public static function getAverageConfidenceByMode(string $thinkingMode): float
    {
        return self::where('thinking_mode', $thinkingMode)
            ->where('was_successful', true)
            ->avg('confidence_score') ?? 0.0;
    }
}
