/**
 * WIDDX AI - GROK Style Interface
 * Main JavaScript file for the intelligent chat interface
 */

class WiddxGrokInterface {
    constructor() {
        this.config = window.WIDDX_CONFIG;
        this.isTyping = false;
        this.currentSession = null;
        this.messageHistory = [];
        this.activeFeatures = new Set();
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupFeatureToggles();
        this.setupLanguageSupport();
        this.setupAutoResize();
        this.setupKeyboardShortcuts();
        this.loadSession();
        
        console.log('✅ WIDDX GROK Interface initialized');
    }
    
    setupEventListeners() {
        // Chat form submission
        const chatForm = document.getElementById('chat-form');
        if (chatForm) {
            chatForm.addEventListener('submit', (e) => this.handleSubmit(e));
        }
        
        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebar-toggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => this.toggleSidebar());
        }
        
        // Navigation items
        document.querySelectorAll('.grok-nav-item').forEach(item => {
            item.addEventListener('click', (e) => this.handleNavigation(e));
        });
        
        // Feature cards
        document.querySelectorAll('.grok-feature-card').forEach(card => {
            card.addEventListener('click', (e) => this.handleFeatureCard(e));
        });
        
        // Language selector
        const languageSelector = document.getElementById('language-selector');
        if (languageSelector) {
            languageSelector.addEventListener('change', (e) => this.changeLanguage(e.target.value));
        }
        
        // Input field
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            messageInput.addEventListener('input', () => this.handleInputChange());
            messageInput.addEventListener('paste', (e) => this.handlePaste(e));
        }
    }
    
    setupFeatureToggles() {
        document.querySelectorAll('.grok-toggle').forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                const feature = e.currentTarget.dataset.feature;
                this.toggleFeature(feature);
            });
        });
    }
    
    setupLanguageSupport() {
        // Set initial language direction
        this.updateLanguageDirection(this.config.currentLanguage);
    }
    
    setupAutoResize() {
        const textarea = document.getElementById('message-input');
        if (!textarea) return;
        
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 200) + 'px';
        });
    }
    
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Enter to send
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                this.submitMessage();
            }
            
            // Escape to clear input
            if (e.key === 'Escape') {
                const messageInput = document.getElementById('message-input');
                if (messageInput && document.activeElement === messageInput) {
                    messageInput.value = '';
                    messageInput.style.height = 'auto';
                    this.updateSendButton();
                }
            }
            
            // Ctrl/Cmd + K for new chat
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                this.newChat();
            }
        });
    }
    
    handleSubmit(e) {
        e.preventDefault();
        this.submitMessage();
    }
    
    async submitMessage() {
        const messageInput = document.getElementById('message-input');
        const message = messageInput?.value.trim();
        
        if (!message || this.isTyping) return;
        
        // Hide welcome screen
        this.hideWelcome();
        
        // Add user message
        this.addMessage(message, 'user');
        
        // Clear input
        messageInput.value = '';
        messageInput.style.height = 'auto';
        this.updateSendButton();
        
        // Show thinking indicator
        this.showThinking();
        
        try {
            // Prepare request data
            const requestData = {
                message: message,
                session_id: this.currentSession,
                features: Object.fromEntries(this.activeFeatures.entries()),
                language: this.config.currentLanguage,
                think_mode: this.activeFeatures.has('think-mode'),
                search_enabled: this.activeFeatures.has('search'),
                deep_search_enabled: this.activeFeatures.has('deep-search'),
                image_generation_enabled: this.activeFeatures.has('image-gen')
            };
            
            // Send to backend
            const response = await this.sendToBackend(requestData);
            
            // Hide thinking indicator
            this.hideThinking();
            
            // Add assistant response
            this.addMessage(response.message, 'assistant', response);
            
            // Update session
            if (response.session_id) {
                this.currentSession = response.session_id;
            }
            
        } catch (error) {
            console.error('Error sending message:', error);
            this.hideThinking();
            this.addMessage('Sorry, I encountered an error. Please try again.', 'assistant', { error: true });
        }
    }
    
    async sendToBackend(data) {
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': this.config.csrfToken,
                'Accept': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    }
    
    addMessage(content, sender, metadata = {}) {
        const messagesContainer = document.getElementById('messages-container');
        if (!messagesContainer) return;
        
        const messageElement = this.createMessageElement(content, sender, metadata);
        messagesContainer.appendChild(messageElement);
        
        // Show messages container
        messagesContainer.style.display = 'flex';
        
        // Scroll to bottom
        this.scrollToBottom();
        
        // Save to history
        this.messageHistory.push({
            content,
            sender,
            metadata,
            timestamp: Date.now()
        });
        
        // Update session
        this.saveSession();
    }
    
    createMessageElement(content, sender, metadata = {}) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `grok-message ${sender}`;
        messageDiv.setAttribute('dir', this.getTextDirection(content));
        
        const avatar = document.createElement('div');
        avatar.className = 'grok-message-avatar';
        avatar.textContent = sender === 'user' ? 'U' : 'W';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'grok-message-content';
        
        const bubble = document.createElement('div');
        bubble.className = 'grok-message-bubble';
        bubble.innerHTML = this.formatMessage(content);
        
        const meta = document.createElement('div');
        meta.className = 'grok-message-meta';
        
        const time = document.createElement('span');
        time.className = 'grok-message-time';
        time.textContent = new Date().toLocaleTimeString();
        meta.appendChild(time);
        
        // Add feature indicators
        if (metadata.features) {
            const features = document.createElement('span');
            features.textContent = `• ${Object.keys(metadata.features).join(', ')}`;
            meta.appendChild(features);
        }
        
        // Add actions
        const actions = document.createElement('div');
        actions.className = 'grok-message-actions';
        
        const copyBtn = document.createElement('button');
        copyBtn.className = 'grok-message-action';
        copyBtn.innerHTML = '<i class="fas fa-copy"></i>';
        copyBtn.title = 'Copy message';
        copyBtn.addEventListener('click', () => this.copyMessage(content));
        actions.appendChild(copyBtn);
        
        meta.appendChild(actions);
        messageContent.appendChild(bubble);
        messageContent.appendChild(meta);
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);
        
        return messageDiv;
    }
    
    formatMessage(content) {
        // Basic markdown-like formatting
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }
    
    toggleFeature(feature) {
        const toggle = document.querySelector(`[data-feature="${feature}"]`);
        if (!toggle) return;
        
        if (this.activeFeatures.has(feature)) {
            this.activeFeatures.delete(feature);
            toggle.classList.remove('active');
        } else {
            this.activeFeatures.add(feature);
            toggle.classList.add('active');
        }
        
        // Update all toggles for this feature
        document.querySelectorAll(`[data-feature="${feature}"]`).forEach(el => {
            if (this.activeFeatures.has(feature)) {
                el.classList.add('active');
            } else {
                el.classList.remove('active');
            }
        });
        
        console.log(`Feature ${feature} ${this.activeFeatures.has(feature) ? 'enabled' : 'disabled'}`);
    }
    
    changeLanguage(language) {
        this.config.currentLanguage = language;
        this.updateLanguageDirection(language);
        this.updatePlaceholders(language);
        
        console.log(`Language changed to: ${language}`);
    }
    
    updateLanguageDirection(language) {
        const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
        const direction = rtlLanguages.includes(language) ? 'rtl' : 'ltr';
        
        document.getElementById('app-html').setAttribute('dir', direction);
        document.getElementById('message-input').setAttribute('dir', 'auto');
    }
    
    updatePlaceholders(language) {
        const placeholders = {
            en: 'Ask WIDDX anything... (Ctrl+Enter to send)',
            ar: 'اسأل WIDDX أي شيء... (Ctrl+Enter للإرسال)',
            es: 'Pregunta a WIDDX cualquier cosa... (Ctrl+Enter para enviar)',
            fr: 'Demandez à WIDDX n\'importe quoi... (Ctrl+Enter pour envoyer)',
            de: 'Fragen Sie WIDDX alles... (Ctrl+Enter zum Senden)',
            zh: '向WIDDX询问任何问题... (Ctrl+Enter发送)',
            ja: 'WIDDXに何でも聞いてください... (Ctrl+Enterで送信)',
            ko: 'WIDDX에게 무엇이든 물어보세요... (Ctrl+Enter로 전송)',
            ru: 'Спросите WIDDX о чем угодно... (Ctrl+Enter для отправки)'
        };
        
        const messageInput = document.getElementById('message-input');
        if (messageInput && placeholders[language]) {
            messageInput.placeholder = placeholders[language];
        }
    }
    
    getTextDirection(text) {
        // Simple RTL detection
        const rtlChars = /[\u0590-\u083F]|[\u08A0-\u08FF]|[\uFB1D-\uFDFF]|[\uFE70-\uFEFF]/;
        return rtlChars.test(text) ? 'rtl' : 'ltr';
    }
    
    showThinking() {
        this.isTyping = true;
        const thinkingIndicator = document.getElementById('thinking-indicator');
        if (thinkingIndicator) {
            thinkingIndicator.classList.remove('hidden');
            this.scrollToBottom();
        }
    }
    
    hideThinking() {
        this.isTyping = false;
        const thinkingIndicator = document.getElementById('thinking-indicator');
        if (thinkingIndicator) {
            thinkingIndicator.classList.add('hidden');
        }
    }
    
    hideWelcome() {
        const welcomeScreen = document.getElementById('welcome-screen');
        if (welcomeScreen) {
            welcomeScreen.style.display = 'none';
        }
    }
    
    scrollToBottom() {
        const chatArea = document.getElementById('chat-area');
        if (chatArea) {
            chatArea.scrollTop = chatArea.scrollHeight;
        }
    }
    
    handleInputChange() {
        this.updateSendButton();
    }
    
    updateSendButton() {
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        
        if (messageInput && sendButton) {
            sendButton.disabled = !messageInput.value.trim();
        }
    }
    
    handleNavigation(e) {
        const action = e.currentTarget.dataset.action;
        const feature = e.currentTarget.dataset.feature;
        
        if (action) {
            this.handleAction(action);
        } else if (feature) {
            this.toggleFeature(feature);
        }
    }
    
    handleAction(action) {
        switch (action) {
            case 'new-chat':
                this.newChat();
                break;
            case 'chat-history':
                this.showChatHistory();
                break;
            case 'language':
                this.showLanguageSettings();
                break;
            case 'preferences':
                this.showPreferences();
                break;
        }
    }
    
    handleFeatureCard(e) {
        const feature = e.currentTarget.dataset.feature;
        if (feature) {
            this.toggleFeature(feature);
        }
    }
    
    newChat() {
        this.currentSession = null;
        this.messageHistory = [];
        this.activeFeatures.clear();
        
        // Clear messages
        const messagesContainer = document.getElementById('messages-container');
        if (messagesContainer) {
            messagesContainer.innerHTML = '';
            messagesContainer.style.display = 'none';
        }
        
        // Show welcome screen
        const welcomeScreen = document.getElementById('welcome-screen');
        if (welcomeScreen) {
            welcomeScreen.style.display = 'flex';
        }
        
        // Clear input
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            messageInput.value = '';
            messageInput.style.height = 'auto';
        }
        
        // Update toggles
        document.querySelectorAll('.grok-toggle').forEach(toggle => {
            toggle.classList.remove('active');
        });
        
        this.updateSendButton();
        console.log('New chat started');
    }
    
    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.classList.toggle('open');
        }
    }
    
    copyMessage(content) {
        navigator.clipboard.writeText(content).then(() => {
            console.log('Message copied to clipboard');
        });
    }
    
    saveSession() {
        if (this.currentSession) {
            localStorage.setItem(`widdx-session-${this.currentSession}`, JSON.stringify({
                messages: this.messageHistory.slice(-50), // Keep last 50 messages
                features: Array.from(this.activeFeatures),
                language: this.config.currentLanguage,
                timestamp: Date.now()
            }));
        }
    }
    
    loadSession() {
        // Load last session or create new one
        const lastSession = localStorage.getItem('widdx-last-session');
        if (lastSession) {
            try {
                const sessionData = JSON.parse(localStorage.getItem(`widdx-session-${lastSession}`));
                if (sessionData) {
                    this.currentSession = lastSession;
                    this.messageHistory = sessionData.messages || [];
                    this.activeFeatures = new Set(sessionData.features || []);
                    
                    // Restore messages
                    if (this.messageHistory.length > 0) {
                        this.hideWelcome();
                        this.messageHistory.forEach(msg => {
                            this.addMessage(msg.content, msg.sender, msg.metadata);
                        });
                    }
                }
            } catch (error) {
                console.warn('Failed to load session:', error);
            }
        }
    }
    
    showChatHistory() {
        console.log('Show chat history - TODO');
    }
    
    showLanguageSettings() {
        console.log('Show language settings - TODO');
    }
    
    showPreferences() {
        console.log('Show preferences - TODO');
    }
    
    handlePaste(e) {
        // Handle image paste in the future
        console.log('Paste event detected');
    }
}

// Initialize the interface
function initializeGrokInterface() {
    window.widdxGrok = new WiddxGrokInterface();
}

// Export for global access
window.WiddxGrokInterface = WiddxGrokInterface;
window.initializeGrokInterface = initializeGrokInterface;
